<resources>
    <string name="app_name">Robot STEM Simple</string>
    
    <!-- Tab titles -->
    <string name="tab_control">Control</string>
    <string name="tab_ai">Asistente IA</string>
    <string name="tab_settings">Configuración</string>
    
    <!-- Control tab -->
    <string name="movement_controls">Controles de Movimiento</string>
    <string name="servo_controls">Controles de Servo</string>
    <string name="sensor_status">Estado del Sensor</string>
    <string name="forward">Adelante</string>
    <string name="backward">Atrás</string>
    <string name="left">Iz<PERSON>erda</string>
    <string name="right">Derecha</string>
    <string name="stop">Parar</string>
    <string name="wave">Saludar</string>
    <string name="point">Señalar</string>
    <string name="rest">Descanso</string>
    <string name="distance">Distancia: %1$s cm</string>
    <string name="face_count">Caras: %1$d</string>
    
    <!-- AI tab -->
    <string name="ai_assistant">Asistente IA</string>
    <string name="speak_command">Habla tu comando</string>
    <string name="type_command">Escribe tu comando</string>
    <string name="send">Enviar</string>
    <string name="listening">Escuchando...</string>
    <string name="processing">Procesando...</string>
    <string name="preset_created">Preset creado: %1$s</string>
    <string name="preset_executed">Ejecutando preset: %1$s</string>
    
    <!-- Settings tab -->
    <string name="robot_settings">Configuración del Robot</string>
    <string name="ai_settings">Configuración de IA</string>
    <string name="greeting_settings">Configuración de Saludo Inteligente</string>
    <string name="esp32_ip">Dirección IP del ESP32</string>
    <string name="greeting_enabled">Habilitar Saludo Inteligente</string>
    <string name="greeting_distance">Distancia de Saludo (cm)</string>
    <string name="greeting_cooldown">Tiempo de Espera de Saludo (segundos)</string>
    <string name="ai_wake_word">Palabra de Activación de IA</string>
    <string name="language">Idioma</string>
    <string name="voice_gender">Género de Voz</string>
    <string name="male">Masculino</string>
    <string name="female">Femenino</string>
    
    <!-- Status messages -->
    <string name="robot_online">Robot Conectado</string>
    <string name="robot_offline">Robot Desconectado</string>
    <string name="connecting">Conectando...</string>
    <string name="connection_failed">Conexión Fallida</string>
    <string name="camera_permission_required">Se requiere permiso de cámara</string>
    <string name="microphone_permission_required">Se requiere permiso de micrófono</string>
    
    <!-- Error messages -->
    <string name="error_camera_init">Error al inicializar la cámara</string>
    <string name="error_ai_service">Servicio de IA no disponible</string>
    <string name="error_robot_communication">Error de comunicación del robot</string>
    <string name="error_invalid_command">Comando inválido</string>
</resources>
