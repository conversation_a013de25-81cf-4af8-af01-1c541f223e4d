package com.stemrobo.simple.utils;

import android.util.Log;

/**
 * Global manager for stopping video playback from voice commands
 */
public class VideoStopManager {
    private static final String TAG = "VideoStopManager";
    private static VideoStopManager instance;
    private VideoStopCallback currentVideoCallback;
    
    public interface VideoStopCallback {
        void onStopRequested();
    }
    
    private VideoStopManager() {}
    
    public static synchronized VideoStopManager getInstance() {
        if (instance == null) {
            instance = new VideoStopManager();
        }
        return instance;
    }
    
    public void setCurrentVideoCallback(VideoStopCallback callback) {
        this.currentVideoCallback = callback;
        Log.d(TAG, "Video stop callback registered");
    }
    
    public void clearCurrentVideoCallback() {
        this.currentVideoCallback = null;
        Log.d(TAG, "Video stop callback cleared");
    }
    
    public void requestVideoStop() {
        if (currentVideoCallback != null) {
            Log.d(TAG, "Requesting video stop");
            currentVideoCallback.onStopRequested();
        } else {
            Log.d(TAG, "No video callback available for stop request");
        }
    }
    
    public boolean hasActiveVideo() {
        return currentVideoCallback != null;
    }
}
