<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="black">#FF000000</color>
    <color name="white">#FFFFFFFF</color>
    
    <!-- Custom colors for STEM Simple Robot -->
    <color name="primary_blue">#FF2196F3</color>
    <color name="primary_blue_dark">#FF1976D2</color>
    <color name="accent_orange">#FFFF9800</color>
    <color name="accent_orange_dark">#FFF57C00</color>
    
    <color name="background_primary">#FFF5F5F5</color>
    <color name="background_light">#FFF5F5F5</color>
    <color name="surface_light">#FFFFFFFF</color>
    <color name="border_light">#FFE0E0E0</color>
    <color name="text_primary">#FF212121</color>
    <color name="text_secondary">#FF757575</color>
    
    <!-- Status colors -->
    <color name="status_online">#FF4CAF50</color>
    <color name="status_offline">#FFF44336</color>
    <color name="status_connecting">#FFFF9800</color>
    
    <!-- Button colors -->
    <color name="button_primary">#FF2196F3</color>
    <color name="button_secondary">#FF9E9E9E</color>
    <color name="button_danger">#FFF44336</color>
    <color name="button_success">#FF4CAF50</color>

    <!-- Additional colors for compatibility -->
    <color name="robot_primary">#FF2196F3</color>
    <color name="robot_accent">#FFFF9800</color>
    <color name="robot_background">#FFF5F5F5</color>
    <color name="robot_face_background">#FFFFFFFF</color>
    <color name="robot_eye_color">#FF2196F3</color>
    <color name="control_button_background">#FFFFFFFF</color>
    <color name="background_secondary">#FFF0F0F0</color>
    <color name="content_background">#FFFFFFFF</color>
    <color name="text_hint">#FF9E9E9E</color>
    <color name="navigation_item_color">#FF757575</color>
    <color name="status_bar_background">#FF2196F3</color>
    <color name="transcription_text_background">#80000000</color>

    <!-- Listening Indicator Colors -->
    <color name="indicator_active">#FF00FF00</color>
    <color name="indicator_inactive">#FFCCCCCC</color>
</resources>
