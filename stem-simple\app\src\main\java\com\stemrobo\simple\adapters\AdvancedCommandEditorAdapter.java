package com.stemrobo.simple.adapters;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import android.widget.LinearLayout;
import android.widget.Switch;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.stemrobo.simple.R;
import com.stemrobo.simple.ai.PresetStep;
import com.stemrobo.simple.ai.AICommand;

import java.util.List;

/**
 * Advanced adapter for editing preset steps with timeline and multiple actions
 */
public class AdvancedCommandEditorAdapter extends RecyclerView.Adapter<AdvancedCommandEditorAdapter.StepViewHolder> {
    
    private final List<PresetStep> steps;
    private final StepActionListener listener;
    
    public interface StepActionListener {
        void onStepEdit(PresetStep step, int position);
        void onStepDelete(PresetStep step, int position);
        void onStepDuplicate(PresetStep step, int position);
        void onStepMoveUp(PresetStep step, int position);
        void onStepMoveDown(PresetStep step, int position);
        void onStepToggleEnabled(PresetStep step, int position, boolean enabled);
        void onAddActionToStep(PresetStep step, int position);
        void onEditStepAction(PresetStep step, AICommand action, int stepPosition, int actionPosition);
        void onDeleteStepAction(PresetStep step, AICommand action, int stepPosition, int actionPosition);
    }
    
    public AdvancedCommandEditorAdapter(List<PresetStep> steps, StepActionListener listener) {
        this.steps = steps;
        this.listener = listener;
    }
    
    @NonNull
    @Override
    public StepViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_advanced_step_editor, parent, false);
        return new StepViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull StepViewHolder holder, int position) {
        PresetStep step = steps.get(position);
        holder.bind(step, position);
    }
    
    @Override
    public int getItemCount() {
        return steps.size();
    }
    
    class StepViewHolder extends RecyclerView.ViewHolder {
        private final TextView stepNumber;
        private final TextView stepName;
        private final TextView stepTiming;
        private final TextView stepDescription;
        private final Switch stepEnabled;
        private final LinearLayout actionsContainer;
        private final RecyclerView actionsRecyclerView;
        private final Button btnAddAction;
        private final Button btnEditStep;
        private final Button btnDuplicateStep;
        private final Button btnDeleteStep;
        private final Button btnMoveUp;
        private final Button btnMoveDown;
        private final View expandedDetails;
        private final Button btnToggleDetails;
        
        private boolean isExpanded = false;
        
        public StepViewHolder(@NonNull View itemView) {
            super(itemView);
            stepNumber = itemView.findViewById(R.id.step_number);
            stepName = itemView.findViewById(R.id.step_name);
            stepTiming = itemView.findViewById(R.id.step_timing);
            stepDescription = itemView.findViewById(R.id.step_description);
            stepEnabled = itemView.findViewById(R.id.step_enabled);
            actionsContainer = itemView.findViewById(R.id.actions_container);
            actionsRecyclerView = itemView.findViewById(R.id.actions_recycler_view);
            btnAddAction = itemView.findViewById(R.id.btn_add_action);
            btnEditStep = itemView.findViewById(R.id.btn_edit_step);
            btnDuplicateStep = itemView.findViewById(R.id.btn_duplicate_step);
            btnDeleteStep = itemView.findViewById(R.id.btn_delete_step);
            btnMoveUp = itemView.findViewById(R.id.btn_move_up);
            btnMoveDown = itemView.findViewById(R.id.btn_move_down);
            expandedDetails = itemView.findViewById(R.id.expanded_details);
            btnToggleDetails = itemView.findViewById(R.id.btn_toggle_details);
        }
        
        public void bind(PresetStep step, int position) {
            stepNumber.setText(String.valueOf(position + 1));
            stepName.setText(step.getName());
            stepTiming.setText(String.format("%.1fs - %.1fs (%.1fs)", 
                step.getStartTimeSeconds(), 
                step.getEndTimeSeconds(), 
                step.getDurationSeconds()));
            
            if (step.getDescription() != null && !step.getDescription().isEmpty()) {
                stepDescription.setText(step.getDescription());
                stepDescription.setVisibility(View.VISIBLE);
            } else {
                stepDescription.setVisibility(View.GONE);
            }
            
            stepEnabled.setChecked(step.isEnabled());
            
            // Set up actions list
            setupActionsRecyclerView(step, position);
            
            // Button states
            btnMoveUp.setEnabled(position > 0);
            btnMoveDown.setEnabled(position < steps.size() - 1);
            
            // Click listeners
            setupClickListeners(step, position);
            
            // Update expanded state
            expandedDetails.setVisibility(isExpanded ? View.VISIBLE : View.GONE);
            btnToggleDetails.setText(isExpanded ? "▼" : "▶");
        }
        
        private void setupActionsRecyclerView(PresetStep step, int stepPosition) {
            if (step.getActions().isEmpty()) {
                actionsRecyclerView.setVisibility(View.GONE);
                return;
            }
            
            actionsRecyclerView.setVisibility(View.VISIBLE);
            
            // Create simple adapter for actions
            ActionListAdapter actionAdapter = new ActionListAdapter(step.getActions(), new ActionListAdapter.ActionClickListener() {
                @Override
                public void onActionEdit(AICommand action, int actionPosition) {
                    if (listener != null) {
                        listener.onEditStepAction(step, action, stepPosition, actionPosition);
                    }
                }
                
                @Override
                public void onActionDelete(AICommand action, int actionPosition) {
                    if (listener != null) {
                        listener.onDeleteStepAction(step, action, stepPosition, actionPosition);
                    }
                }
            });
            
            actionsRecyclerView.setLayoutManager(new LinearLayoutManager(itemView.getContext()));
            actionsRecyclerView.setAdapter(actionAdapter);
        }
        
        private void setupClickListeners(PresetStep step, int position) {
            stepEnabled.setOnCheckedChangeListener((buttonView, isChecked) -> {
                if (listener != null) {
                    listener.onStepToggleEnabled(step, position, isChecked);
                }
            });
            
            btnToggleDetails.setOnClickListener(v -> {
                isExpanded = !isExpanded;
                expandedDetails.setVisibility(isExpanded ? View.VISIBLE : View.GONE);
                btnToggleDetails.setText(isExpanded ? "▼" : "▶");
            });
            
            btnAddAction.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onAddActionToStep(step, position);
                }
            });
            
            btnEditStep.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onStepEdit(step, position);
                }
            });
            
            btnDuplicateStep.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onStepDuplicate(step, position);
                }
            });
            
            btnDeleteStep.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onStepDelete(step, position);
                }
            });
            
            btnMoveUp.setOnClickListener(v -> {
                if (listener != null && position > 0) {
                    listener.onStepMoveUp(step, position);
                }
            });
            
            btnMoveDown.setOnClickListener(v -> {
                if (listener != null && position < steps.size() - 1) {
                    listener.onStepMoveDown(step, position);
                }
            });
            
            // Click on item to toggle details
            itemView.setOnClickListener(v -> btnToggleDetails.performClick());
        }
    }
    
    // Simple adapter for actions within a step
    private static class ActionListAdapter extends RecyclerView.Adapter<ActionListAdapter.ActionViewHolder> {
        private final List<AICommand> actions;
        private final ActionClickListener listener;
        
        interface ActionClickListener {
            void onActionEdit(AICommand action, int position);
            void onActionDelete(AICommand action, int position);
        }
        
        ActionListAdapter(List<AICommand> actions, ActionClickListener listener) {
            this.actions = actions;
            this.listener = listener;
        }
        
        @NonNull
        @Override
        public ActionViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.item_step_action, parent, false);
            return new ActionViewHolder(view);
        }
        
        @Override
        public void onBindViewHolder(@NonNull ActionViewHolder holder, int position) {
            AICommand action = actions.get(position);
            holder.bind(action, position);
        }
        
        @Override
        public int getItemCount() {
            return actions.size();
        }
        
        class ActionViewHolder extends RecyclerView.ViewHolder {
            private final TextView actionType;
            private final TextView actionDetails;
            private final Button btnEditAction;
            private final Button btnDeleteAction;
            
            ActionViewHolder(@NonNull View itemView) {
                super(itemView);
                actionType = itemView.findViewById(R.id.action_type);
                actionDetails = itemView.findViewById(R.id.action_details);
                btnEditAction = itemView.findViewById(R.id.btn_edit_action);
                btnDeleteAction = itemView.findViewById(R.id.btn_delete_action);
            }
            
            void bind(AICommand action, int position) {
                actionType.setText(action.getType());
                
                String details = action.getAction();
                if (action.getDuration() > 0) {
                    details += " (" + action.getDuration() + "ms)";
                }
                if (action.getParameter() != null && !action.getParameter().isEmpty()) {
                    details += " - " + action.getParameter();
                }
                actionDetails.setText(details);
                
                btnEditAction.setOnClickListener(v -> {
                    if (listener != null) {
                        listener.onActionEdit(action, position);
                    }
                });
                
                btnDeleteAction.setOnClickListener(v -> {
                    if (listener != null) {
                        listener.onActionDelete(action, position);
                    }
                });
            }
        }
    }
}
