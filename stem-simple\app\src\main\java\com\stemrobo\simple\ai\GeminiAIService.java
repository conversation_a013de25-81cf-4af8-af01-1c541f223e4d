package com.stemrobo.simple.ai;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import java.io.IOException;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import com.stemrobo.simple.utils.LanguageManager;

/**
 * Enhanced AI Service using Google Gemini
 * Provides intelligent robot control with structured responses and action execution
 */
public class GeminiAIService {
    private static final String TAG = "GeminiAIService";
    
    // Gemini API Configuration
    private static final String GEMINI_API_KEY = "AIzaSyDP2FAqAXkaoVcuQekdAmXSV8FI5A3zYWY";
    private static final String BASE_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent";
    
    // Context and conversation management
    private final Context context;
    private final SharedPreferences preferences;
    private final OkHttpClient httpClient;
    private final Executor executor;
    private final LanguageManager languageManager;
    
    // Conversation history
    private final List<ConversationEntry> conversationHistory;
    private int maxContextEntries = 10; // Configurable context memory
    
    // Callbacks
    private AIResponseCallback responseCallback;
    private ActionExecutionCallback actionCallback;
    
    public interface AIResponseCallback {
        void onResponse(String response, List<RobotAction> actions);
        void onError(String error);
    }
    
    public interface ActionExecutionCallback {
        void executeMovement(String direction, int duration);
        void executeServo(String servo, int position);
        void executeGesture(String gesture);
        void executeHeadMovement(String direction);
        void executeLMSAction(String action);
        void executeLMSAction(String action, int classNumber);
        void executePreset(String presetName);
        void speakText(String text);
    }
    
    public static class ConversationEntry {
        public final String userInput;
        public final String aiResponse;
        public final long timestamp;
        
        public ConversationEntry(String userInput, String aiResponse) {
            this.userInput = userInput;
            this.aiResponse = aiResponse;
            this.timestamp = System.currentTimeMillis();
        }
    }
    
    public static class RobotAction {
        public final String type; // "movement", "servo", "gesture", "preset", "speak"
        public final String action;
        public final int value; // For servo positions, movement duration
        public final String text; // For speech
        
        public RobotAction(String type, String action, int value, String text) {
            this.type = type;
            this.action = action;
            this.value = value;
            this.text = text;
        }
        
        public RobotAction(String type, String action) {
            this(type, action, 0, null);
        }

        public RobotAction(String type, String text, boolean isText) {
            this(type, null, 0, text);
        }
    }
    
    public GeminiAIService(Context context) {
        this.context = context;
        this.preferences = context.getSharedPreferences("ai_settings", Context.MODE_PRIVATE);
        this.executor = Executors.newCachedThreadPool();
        this.conversationHistory = new ArrayList<>();
        this.languageManager = new LanguageManager(context);

        // Initialize HTTP client
        this.httpClient = new OkHttpClient();

        // Load settings
        loadSettings();

        Log.d(TAG, "Gemini AI Service initialized");
    }
    
    public void setResponseCallback(AIResponseCallback callback) {
        this.responseCallback = callback;
    }
    
    public void setActionCallback(ActionExecutionCallback callback) {
        this.actionCallback = callback;
    }
    
    // ===== SETTINGS MANAGEMENT =====

    private void loadSettings() {
        maxContextEntries = preferences.getInt("context_entries", 10);
    }

    public void setContextMemorySize(int size) {
        maxContextEntries = Math.max(1, Math.min(50, size)); // Limit between 1-50
        preferences.edit().putInt("context_entries", maxContextEntries).apply();

        // Trim conversation history if needed
        while (conversationHistory.size() > maxContextEntries) {
            conversationHistory.remove(0);
        }

        Log.d(TAG, "Context memory size set to: " + maxContextEntries);
    }

    public int getContextMemorySize() {
        return maxContextEntries;
    }

    public void setAIPersonality(String personality) {
        preferences.edit().putString("ai_personality", personality).apply();
        Log.d(TAG, "AI personality set to: " + personality);
    }

    public String getAIPersonality() {
        return preferences.getString("ai_personality", "helpful_assistant");
    }

    public void setResponseStyle(String style) {
        preferences.edit().putString("response_style", style).apply();
        Log.d(TAG, "Response style set to: " + style);
    }

    public String getResponseStyle() {
        return preferences.getString("response_style", "conversational");
    }

    public void setVerboseMode(boolean verbose) {
        preferences.edit().putBoolean("verbose_mode", verbose).apply();
        Log.d(TAG, "Verbose mode " + (verbose ? "enabled" : "disabled"));
    }

    public boolean isVerboseMode() {
        return preferences.getBoolean("verbose_mode", false);
    }

    // Movement duration settings
    public void setMovementDuration(String direction, int seconds) {
        preferences.edit().putInt("movement_" + direction + "_duration", seconds).apply();
        Log.d(TAG, "Movement duration for " + direction + " set to " + seconds + " seconds");
    }

    public int getMovementDuration(String direction) {
        // Default durations: forward/backward = 3 seconds, left/right = 2 seconds
        int defaultDuration = (direction.equals("forward") || direction.equals("backward")) ? 3 : 2;
        return preferences.getInt("movement_" + direction + "_duration", defaultDuration);
    }

    private int extractClassNumber(String command) {
        String lowerCommand = command.toLowerCase();

        // Look for patterns like "class 1", "class 2", etc.
        if (lowerCommand.contains("class")) {
            String[] words = lowerCommand.split("\\s+");
            for (int i = 0; i < words.length - 1; i++) {
                if (words[i].equals("class")) {
                    try {
                        return Integer.parseInt(words[i + 1]);
                    } catch (NumberFormatException e) {
                        // Continue searching
                    }
                }
            }
        }

        // Look for standalone numbers 1-12
        String[] words = lowerCommand.split("\\s+");
        for (String word : words) {
            try {
                int number = Integer.parseInt(word);
                if (number >= 1 && number <= 12) {
                    return number;
                }
            } catch (NumberFormatException e) {
                // Continue searching
            }
        }

        return 1; // Default to class 1
    }

    private String extractPresetName(String command) {
        String lowerCommand = command.toLowerCase();

        // Look for patterns like "run preset test", "execute preset demo", etc.
        if (lowerCommand.contains("preset")) {
            String[] words = lowerCommand.split("\\s+");
            for (int i = 0; i < words.length - 1; i++) {
                if (words[i].equals("preset")) {
                    if (i + 1 < words.length) {
                        return words[i + 1];
                    }
                }
            }
        }

        // Look for common preset names
        if (lowerCommand.contains("demo")) return "Demo";
        if (lowerCommand.contains("greeting")) return "Greeting";
        if (lowerCommand.contains("patrol")) return "Patrol";
        if (lowerCommand.contains("test")) return "test";

        return "Demo"; // Default preset
    }
    
    // ===== AI PROCESSING =====
    
    public void processInput(String userInput) {
        if (userInput == null || userInput.trim().isEmpty()) {
            if (responseCallback != null) {
                responseCallback.onError("Empty input");
            }
            return;
        }
        
        executor.execute(() -> {
            try {
                String prompt = buildPrompt(userInput);
                Log.d(TAG, "Sending prompt to Gemini: " + prompt);

                String response = callGeminiAPI(userInput);
                processAIResponse(userInput, response);

            } catch (Exception e) {
                Log.e(TAG, "Error calling Gemini API", e);
                if (responseCallback != null) {
                    responseCallback.onError("AI processing failed: " + e.getMessage());
                }
            }
        });
    }
    
    private String buildPrompt(String userInput) {
        StringBuilder prompt = new StringBuilder();

        // System instructions with personality
        String personality = getAIPersonality();
        String responseStyle = getResponseStyle();
        boolean verbose = isVerboseMode();

        prompt.append("You are the AI brain of a STEM educational robot with a ").append(personality).append(" personality. ");
        prompt.append("Your response style should be ").append(responseStyle).append(". ");
        if (verbose) {
            prompt.append("Provide detailed explanations of your actions. ");
        } else {
            prompt.append("Keep responses concise and focused. ");
        }
        prompt.append("You can control robot movement, servo positions, gestures, and speech. sometimes the sentence will be wrong understand it correctly and give the response for example who left should be considered as move left things like that  ");
        prompt.append("Always respond with a JSON object containing 'response' (natural language) and 'actions' (array of robot commands).\n\n");
        
        // Robot capabilities
        prompt.append("ROBOT CAPABILITIES:\n");
        prompt.append("- Movement: forward, backward, left, right, stop (with duration in seconds)\n");
        prompt.append("- Servos: left_arm (0-180°), right_arm (0-180°), head (0-180°)\n");
        prompt.append("- Gestures: wave, point, handshake, rest, look_left, look_center, look_right\n");
        prompt.append("- Presets: custom sequences you can create and execute\n");
        prompt.append("- Speech: text-to-speech for responses\n\n");
        
        // Response format
        prompt.append("RESPONSE FORMAT:\n");
        prompt.append("{\n");
        prompt.append("  \"response\": \"Natural language response to user\",\n");
        prompt.append("  \"actions\": [\n");
        prompt.append("    {\"type\": \"movement\", \"action\": \"forward\", \"duration\": 3},\n");
        prompt.append("    {\"type\": \"servo\", \"action\": \"left_arm\", \"position\": 90},\n");
        prompt.append("    {\"type\": \"gesture\", \"action\": \"wave\"},\n");
        prompt.append("    {\"type\": \"speak\", \"text\": \"Hello there!\"}\n");
        prompt.append("  ]\n");
        prompt.append("}\n\n");
        
        // Conversation context
        if (!conversationHistory.isEmpty()) {
            prompt.append("CONVERSATION HISTORY:\n");
            for (ConversationEntry entry : conversationHistory) {
                prompt.append("User: ").append(entry.userInput).append("\n");
                prompt.append("Robot: ").append(entry.aiResponse).append("\n");
            }
            prompt.append("\n");
        }
        
        // Current user input
        prompt.append("CURRENT USER INPUT: ").append(userInput).append("\n\n");
        prompt.append("Respond with appropriate robot actions and natural language:");
        
        return prompt.toString();
    }
    
    private void processAIResponse(String userInput, String aiResponse) {
        try {
            // Parse structured response format: ACTION: xxx \n RESPONSE: xxx
            String action = "NONE";
            String responseText = aiResponse;

            if (aiResponse.contains("ACTION:") && aiResponse.contains("RESPONSE:")) {
                String[] parts = aiResponse.split("\\n");
                for (String part : parts) {
                    part = part.trim();
                    if (part.startsWith("ACTION:")) {
                        action = part.substring(7).trim();
                    } else if (part.startsWith("RESPONSE:")) {
                        responseText = part.substring(9).trim();
                    }
                }
            }

            // Create robot actions based on the parsed action
            List<RobotAction> actions = new ArrayList<>();

            // Add the action if it's not NONE
            if (!action.equals("NONE")) {
                switch (action) {
                    case "MOVE_FORWARD":
                        int forwardDuration = getMovementDuration("forward");
                        actions.add(new RobotAction("movement", "forward", forwardDuration, null));
                        break;
                    case "MOVE_BACKWARD":
                        int backwardDuration = getMovementDuration("backward");
                        actions.add(new RobotAction("movement", "backward", backwardDuration, null));
                        break;
                    case "MOVE_LEFT":
                        int leftDuration = getMovementDuration("left");
                        actions.add(new RobotAction("movement", "left", leftDuration, null));
                        break;
                    case "MOVE_RIGHT":
                        int rightDuration = getMovementDuration("right");
                        actions.add(new RobotAction("movement", "right", rightDuration, null));
                        break;
                    case "STOP":
                        actions.add(new RobotAction("movement", "stop", 0, null));
                        break;
                    case "HANDSHAKE":
                        actions.add(new RobotAction("gesture", "handshake"));
                        break;
                    case "WAVE":
                        actions.add(new RobotAction("gesture", "wave"));
                        break;
                    case "POINT":
                        actions.add(new RobotAction("gesture", "point"));
                        break;
                    case "REST":
                        actions.add(new RobotAction("gesture", "rest"));
                        break;
                    case "HEAD_LEFT":
                        actions.add(new RobotAction("head", "left"));
                        break;
                    case "HEAD_CENTER":
                        actions.add(new RobotAction("head", "center"));
                        break;
                    case "HEAD_RIGHT":
                        actions.add(new RobotAction("head", "right"));
                        break;
                    case "PLAY_LMS_INTRO":
                        actions.add(new RobotAction("lms", "intro"));
                        break;
                    case "PLAY_LMS_CLASS":
                        // Extract class number from the command if available
                        int classNumber = extractClassNumber(userInput);
                        actions.add(new RobotAction("lms", "class", classNumber, null));
                        break;
                    case "RUN_PRESET":
                        // Extract preset name from the command
                        String presetName = extractPresetName(userInput);
                        actions.add(new RobotAction("preset", presetName, 0, null));
                        break;
                }
            }

            // Always add speech action for the response
            if (!responseText.isEmpty()) {
                actions.add(new RobotAction("speak", responseText, true));
            }

            // Add to conversation history
            addToConversationHistory(userInput, responseText);

            // Execute actions
            executeActions(actions);

            // Notify callback
            if (responseCallback != null) {
                responseCallback.onResponse(responseText, actions);
            }

        } catch (Exception e) {
            Log.e(TAG, "Error processing AI response", e);

            // Fallback: treat as plain text response
            addToConversationHistory(userInput, aiResponse);

            List<RobotAction> speechAction = new ArrayList<>();
            speechAction.add(new RobotAction("speak", aiResponse, true));

            if (responseCallback != null) {
                responseCallback.onResponse(aiResponse, speechAction);
            }

            executeActions(speechAction);
        }
    }
    
    private void executeActions(List<RobotAction> actions) {
        if (actionCallback == null) return;
        
        executor.execute(() -> {
            for (RobotAction action : actions) {
                try {
                    switch (action.type) {
                        case "movement":
                            // Use the duration already set in the action
                            int duration = action.value > 0 ? action.value : getMovementDuration(action.action);
                            actionCallback.executeMovement(action.action, duration);
                            Thread.sleep(duration * 1000L); // Wait for movement duration
                            // Send stop command after movement duration for safety
                            actionCallback.executeMovement("stop", 0);
                            break;

                        case "head":
                            actionCallback.executeHeadMovement(action.action);
                            Thread.sleep(500); // Brief pause for head movement
                            break;

                        case "lms":
                            if (action.value > 0) {
                                actionCallback.executeLMSAction(action.action, action.value);
                            } else {
                                actionCallback.executeLMSAction(action.action);
                            }
                            Thread.sleep(1000); // Brief pause for LMS action
                            break;
                            
                        case "servo":
                            actionCallback.executeServo(action.action, action.value);
                            Thread.sleep(500); // Brief pause for servo movement
                            break;
                            
                        case "gesture":
                            actionCallback.executeGesture(action.action);
                            Thread.sleep(2000); // Wait for gesture completion
                            break;
                            
                        case "preset":
                            actionCallback.executePreset(action.action);
                            Thread.sleep(2000); // Brief pause for preset execution
                            break;

                        case "speak":
                            actionCallback.speakText(action.text);
                            Thread.sleep(1000); // Brief pause for speech
                            break;
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    Log.e(TAG, "Error executing action: " + action.type, e);
                }
            }
        });
    }
    
    private String callGeminiAPI(String userInput) throws IOException, JSONException {
        JSONObject requestJson = new JSONObject();
        JSONArray contents = new JSONArray();

        // Add system prompt as first message
        JSONObject systemContent = new JSONObject();
        JSONArray systemParts = new JSONArray();
        JSONObject systemPart = new JSONObject();
        // Get language-specific system prompt
        String baseSystemPrompt = languageManager.getSystemPrompt();
        String languageInstruction = languageManager.getLanguageInstruction();

        String systemPrompt = baseSystemPrompt + " I have a physical body with wheels for movement, servo motors for arm and head control, and can perform various gestures. " +
            "I must respond in a structured format with two parts:\n\n" +
            "ACTION: [command to execute - ALWAYS IN ENGLISH]\n" +
            "RESPONSE: [text to speak - in the user's language]\n\n" +
            "IMPORTANT: " + languageInstruction + "\n\n" +
            "Available ACTIONS:\n" +
            "- MOVE_FORWARD: Move forward\n" +
            "- MOVE_BACKWARD: Move backward\n" +
            "- MOVE_LEFT: Turn left\n" +
            "- MOVE_RIGHT: Turn right\n" +
            "- STOP: Stop movement\n" +
            "- HANDSHAKE: Perform handshake gesture\n" +
            "- WAVE: Wave hand\n" +
            "- POINT: Point gesture\n" +
            "- REST: Return to rest position\n" +
            "- HEAD_LEFT: Turn head left\n" +
            "- HEAD_CENTER: Center head\n" +
            "- HEAD_RIGHT: Turn head right\n" +
            "- PLAY_LMS_INTRO: Play LMS introduction video\n" +
            "- PLAY_LMS_CLASS: Play LMS class video\n" +
            "- RUN_PRESET: Execute a saved preset by name\n" +
            "- NONE: No physical action needed\n\n" +
            "When users ask me to move (forward, front, ahead, backward, back, left, right), I should use the appropriate MOVE_ action. " +
            "For greetings or social interactions, I should use gestures like HANDSHAKE or WAVE. " +
            "When users ask about LMS, introduction, or educational content, I should use PLAY_LMS_INTRO. " +
            "When users ask about specific classes or lessons (class 1, class 2, etc.), I should use PLAY_LMS_CLASS. " +
            "For class-specific requests, extract the class number and include it in the action. " +
            "The RESPONSE should be natural speech without special characters, optimized for text-to-speech. " +
            "I represent STEM-Xpert's commitment to robotics education and our comprehensive LMS platform.";
        systemPart.put("text", systemPrompt);
        systemParts.put(systemPart);
        systemContent.put("parts", systemParts);
        systemContent.put("role", "user");
        contents.put(systemContent);

        // Add user input as second message
        JSONObject userContent = new JSONObject();
        JSONArray userParts = new JSONArray();
        JSONObject userPart = new JSONObject();
        userPart.put("text", userInput);
        userParts.put(userPart);
        userContent.put("parts", userParts);
        userContent.put("role", "user");
        contents.put(userContent);

        requestJson.put("contents", contents);

        RequestBody body = RequestBody.create(
            requestJson.toString(),
            MediaType.get("application/json; charset=utf-8")
        );

        Request request = new Request.Builder()
            .url(BASE_URL + "?key=" + GEMINI_API_KEY)
            .post(body)
            .addHeader("Content-Type", "application/json")
            .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected response code: " + response);
            }

            String responseBody = response.body().string();
            JSONObject responseJson = new JSONObject(responseBody);

            JSONArray candidates = responseJson.getJSONArray("candidates");
            if (candidates.length() > 0) {
                JSONObject candidate = candidates.getJSONObject(0);
                JSONObject responseContent = candidate.getJSONObject("content");
                JSONArray responseParts = responseContent.getJSONArray("parts");
                if (responseParts.length() > 0) {
                    JSONObject responsePart = responseParts.getJSONObject(0);
                    return responsePart.getString("text");
                }
            }

            return "I'm sorry, I couldn't generate a response right now.";
        }
    }

    private void addToConversationHistory(String userInput, String aiResponse) {
        conversationHistory.add(new ConversationEntry(userInput, aiResponse));

        // Trim history if it exceeds max size
        while (conversationHistory.size() > maxContextEntries) {
            conversationHistory.remove(0);
        }
    }
    
    // ===== CONVERSATION MANAGEMENT =====
    
    public void clearConversationHistory() {
        conversationHistory.clear();
        Log.d(TAG, "Conversation history cleared");
    }
    
    public List<ConversationEntry> getConversationHistory() {
        return new ArrayList<>(conversationHistory);
    }
    
    public void cleanup() {
        if (executor != null && executor instanceof java.util.concurrent.ExecutorService) {
            ((java.util.concurrent.ExecutorService) executor).shutdown();
        }
        Log.d(TAG, "Gemini AI Service cleaned up");
    }
}
