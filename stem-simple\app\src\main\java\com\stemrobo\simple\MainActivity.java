package com.stemrobo.simple;

import android.Manifest;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.util.Log;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import java.util.concurrent.atomic.AtomicInteger;
import androidx.camera.view.PreviewView;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.viewpager2.adapter.FragmentStateAdapter;
import androidx.viewpager2.widget.ViewPager2;
import com.google.android.material.tabs.TabLayout;
import com.google.android.material.tabs.TabLayoutMediator;
import com.stemrobo.simple.robot.SimpleRobotController;
import com.stemrobo.simple.vision.SimpleFaceDetection;
import com.stemrobo.simple.vision.SmartGreeting;
import com.stemrobo.simple.ui.ControlFragment;
import com.stemrobo.simple.ui.AIFragment;
import com.stemrobo.simple.ui.SettingsFragment;
import com.stemrobo.simple.ui.PresetFragment;

/**
 * Main Activity for STEM Simple Robot Application
 * Features simplified architecture with 3 main tabs:
 * 1. Control - Basic robot movement and servo control
 * 2. AI - Unified AI assistant with voice commands and presets
 * 3. Settings - Configuration for all features
 */
public class MainActivity extends AppCompatActivity {
    private static final String TAG = "MainActivity";
    private static final int PERMISSION_REQUEST_CODE = 1001;
    
    // Required permissions
    private static final String[] REQUIRED_PERMISSIONS = {
        Manifest.permission.CAMERA,
        Manifest.permission.RECORD_AUDIO,
        Manifest.permission.ACCESS_FINE_LOCATION,
        Manifest.permission.ACCESS_COARSE_LOCATION
    };
    
    // UI Components
    private TabLayout tabLayout;
    private ViewPager2 viewPager;
    private TextView connectionStatus;
    private TextView faceCountDisplay;
    private TextView distanceDisplay;
    private PreviewView miniCameraPreview;
    
    // Core Systems
    private SimpleRobotController robotController;
    private SimpleFaceDetection faceDetection;
    private SmartGreeting smartGreeting;
    
    // Tab Fragments
    private ControlFragment controlFragment;
    private AIFragment aiFragment;
    private SettingsFragment settingsFragment;
    private PresetFragment presetFragment;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        
        initializeViews();
        checkPermissions();
    }
    
    private void initializeViews() {
        // Status bar components
        connectionStatus = findViewById(R.id.connection_status);
        faceCountDisplay = findViewById(R.id.face_count_display);
        distanceDisplay = findViewById(R.id.distance_display);
        miniCameraPreview = findViewById(R.id.mini_camera_preview);
        
        // Tab navigation
        tabLayout = findViewById(R.id.tab_layout);
        viewPager = findViewById(R.id.view_pager);
        
        setupTabNavigation();
        updateConnectionStatus("Initializing...", false);
        updateFaceCount(0);
        updateDistance(0.0f);
    }
    
    private void setupTabNavigation() {
        // Create fragments
        controlFragment = new ControlFragment();
        aiFragment = new AIFragment();
        settingsFragment = new SettingsFragment();
        presetFragment = new PresetFragment();
        
        // Setup ViewPager2 with adapter
        TabPagerAdapter adapter = new TabPagerAdapter(this);
        viewPager.setAdapter(adapter);
        
        // Connect TabLayout with ViewPager2
        new TabLayoutMediator(tabLayout, viewPager, (tab, position) -> {
            switch (position) {
                case 0:
                    tab.setText(R.string.tab_control);
                    break;
                case 1:
                    tab.setText(R.string.tab_ai);
                    break;
                case 2:
                    tab.setText("Presets");
                    break;
                case 3:
                    tab.setText(R.string.tab_settings);
                    break;
            }
        }).attach();
    }
    
    private void checkPermissions() {
        boolean allPermissionsGranted = true;
        for (String permission : REQUIRED_PERMISSIONS) {
            if (ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED) {
                allPermissionsGranted = false;
                break;
            }
        }
        
        if (allPermissionsGranted) {
            initializeSystems();
        } else {
            ActivityCompat.requestPermissions(this, REQUIRED_PERMISSIONS, PERMISSION_REQUEST_CODE);
        }
    }
    
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        
        if (requestCode == PERMISSION_REQUEST_CODE) {
            boolean allGranted = true;
            for (int result : grantResults) {
                if (result != PackageManager.PERMISSION_GRANTED) {
                    allGranted = false;
                    break;
                }
            }
            
            if (allGranted) {
                initializeSystems();
            } else {
                updateConnectionStatus("Permissions required", false);
            }
        }
    }
    
    private void initializeSystems() {
        try {
            updateConnectionStatus("Initializing systems...", false);

            // Initialize robot controller with error handling
            try {
                robotController = new SimpleRobotController(this);
                robotController.setStatusCallback(this::updateConnectionStatus);
                robotController.initialize();
                Log.d(TAG, "Robot controller initialized successfully");
            } catch (Exception e) {
                Log.e(TAG, "Failed to initialize robot controller", e);
                updateConnectionStatus("Robot controller failed: " + e.getMessage(), false);
            }

            // Initialize face detection with error handling
            try {
                faceDetection = new SimpleFaceDetection(this, miniCameraPreview);
                faceDetection.setFaceCountCallback(this::updateFaceCount);
                faceDetection.startFaceDetection();
                Log.d(TAG, "Face detection initialized successfully");
            } catch (Exception e) {
                Log.e(TAG, "Failed to initialize face detection", e);
                updateConnectionStatus("Face detection failed: " + e.getMessage(), false);
            }

            // Initialize smart greeting with error handling
            try {
                if (robotController != null) {
                    smartGreeting = new SmartGreeting(this, robotController);
                    smartGreeting.setGreetingCallback(new SmartGreeting.GreetingCallback() {
                        @Override
                        public void onGreetingStarted() {
                            updateConnectionStatus("Smart greeting in progress...", true);
                        }

                        @Override
                        public void onGreetingCompleted() {
                            updateConnectionStatus("Smart greeting completed", true);
                        }

                        @Override
                        public void onGreetingError(String error) {
                            updateConnectionStatus("Greeting error: " + error, true);
                        }
                    });

                    // Connect face detection to smart greeting
                    if (faceDetection != null) {
                        faceDetection.setSmartGreetingCallback(smartGreeting::processFaces);
                    }

                    Log.d(TAG, "Smart greeting initialized successfully");
                }
            } catch (Exception e) {
                Log.e(TAG, "Failed to initialize smart greeting", e);
                updateConnectionStatus("Smart greeting failed: " + e.getMessage(), false);
            }

            // Start distance monitoring with error handling
            try {
                startDistanceMonitoring();
                Log.d(TAG, "Distance monitoring started successfully");
            } catch (Exception e) {
                Log.e(TAG, "Failed to start distance monitoring", e);
            }

            // Final status update
            boolean allSystemsReady = (robotController != null) && (faceDetection != null);
            if (allSystemsReady) {
                updateConnectionStatus("All systems ready", true);
            } else {
                updateConnectionStatus("Some systems failed to initialize", false);
            }

        } catch (Exception e) {
            Log.e(TAG, "Critical error during system initialization", e);
            updateConnectionStatus("Critical initialization error: " + e.getMessage(), false);
        }
    }
    
    // Status update methods
    public void updateConnectionStatus(String status, boolean isConnected) {
        final String finalStatus = (status == null) ? "Unknown status" : status;

        runOnUiThread(() -> {
            try {
                if (connectionStatus != null) {
                    connectionStatus.setText(finalStatus);
                    connectionStatus.setTextColor(getColor(isConnected ? R.color.status_online : R.color.status_offline));
                }
            } catch (Exception e) {
                Log.e(TAG, "Error updating connection status", e);
            }
        });
    }
    
    public void updateFaceCount(int count) {
        runOnUiThread(() -> {
            try {
                if (faceCountDisplay != null) {
                    faceCountDisplay.setText(getString(R.string.face_count, Math.max(0, count)));
                }

                // Update control fragment if it exists
                if (controlFragment != null) {
                    controlFragment.updateFaceCount(count);
                }
            } catch (Exception e) {
                Log.e(TAG, "Error updating face count", e);
            }
        });
    }
    
    public void updateDistance(float distance) {
        runOnUiThread(() -> {
            try {
                // Validate distance value
                float finalDistance = distance;
                if (Float.isNaN(finalDistance) || finalDistance < 0) {
                    finalDistance = 999.0f; // Default "no reading" value
                }

                if (distanceDisplay != null) {
                    distanceDisplay.setText(getString(R.string.distance, String.format("%.1f", finalDistance)));
                }

                // Update control fragment if it exists
                if (controlFragment != null) {
                    controlFragment.updateDistance(finalDistance);
                }

                // Update smart greeting system with current distance
                if (smartGreeting != null) {
                    smartGreeting.updateDistance(finalDistance);
                }
            } catch (Exception e) {
                Log.e(TAG, "Error updating distance", e);
            }
        });
    }

    private Thread distanceMonitoringThread;
    private volatile boolean isDistanceMonitoringActive = false;

    private void startDistanceMonitoring() {
        if (isDistanceMonitoringActive) {
            Log.w(TAG, "Distance monitoring already active");
            return;
        }

        isDistanceMonitoringActive = true;
        distanceMonitoringThread = new Thread(() -> {
            Log.d(TAG, "Distance monitoring thread started");
            AtomicInteger errorCount = new AtomicInteger(0);
            final int MAX_ERRORS = 5;

            while (isDistanceMonitoringActive && !Thread.currentThread().isInterrupted()) {
                try {
                    if (robotController != null && robotController.isConnected()) {
                        robotController.getDistance(new SimpleRobotController.DistanceCallback() {
                            @Override
                            public void onDistanceReceived(float distance) {
                                updateDistance(distance);
                                errorCount.set(0); // Reset error count on success
                            }

                            @Override
                            public void onDistanceError(String error) {
                                int currentErrors = errorCount.incrementAndGet();
                                if (currentErrors >= MAX_ERRORS) {
                                    Log.w(TAG, "Too many distance errors, pausing monitoring");
                                    try {
                                        Thread.sleep(5000); // Pause for 5 seconds
                                        errorCount.set(0); // Reset after pause
                                    } catch (InterruptedException e) {
                                        Thread.currentThread().interrupt();
                                    }
                                }
                            }
                        });
                    } else {
                        // Robot not connected, wait longer
                        Thread.sleep(3000);
                    }

                    Thread.sleep(100); // Update every 0.1 seconds for real-time distance monitoring

                } catch (InterruptedException e) {
                    Log.d(TAG, "Distance monitoring interrupted");
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    Log.e(TAG, "Error in distance monitoring", e);
                    int currentErrors = errorCount.incrementAndGet();
                    if (currentErrors >= MAX_ERRORS) {
                        Log.w(TAG, "Too many errors, pausing distance monitoring");
                        try {
                            Thread.sleep(5000);
                            errorCount.set(0);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                    }
                }
            }

            isDistanceMonitoringActive = false;
            Log.d(TAG, "Distance monitoring thread stopped");
        });

        distanceMonitoringThread.setName("DistanceMonitoring");
        distanceMonitoringThread.start();
    }

    private void stopDistanceMonitoring() {
        isDistanceMonitoringActive = false;
        if (distanceMonitoringThread != null && distanceMonitoringThread.isAlive()) {
            distanceMonitoringThread.interrupt();
            try {
                distanceMonitoringThread.join(2000); // Wait up to 2 seconds
            } catch (InterruptedException e) {
                Log.w(TAG, "Interrupted while stopping distance monitoring");
            }
        }
        Log.d(TAG, "Distance monitoring stopped");
    }
    
    // Getters for fragments to access core systems
    public SimpleRobotController getRobotController() {
        return robotController;
    }
    
    public SimpleFaceDetection getFaceDetection() {
        return faceDetection;
    }

    public SmartGreeting getSmartGreeting() {
        return smartGreeting;
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();

        Log.d(TAG, "MainActivity destroying, cleaning up systems...");

        // Stop distance monitoring first
        stopDistanceMonitoring();

        // Cleanup robot controller
        if (robotController != null) {
            try {
                robotController.cleanup();
                Log.d(TAG, "Robot controller cleaned up");
            } catch (Exception e) {
                Log.e(TAG, "Error cleaning up robot controller", e);
            }
        }

        // Cleanup face detection
        if (faceDetection != null) {
            try {
                faceDetection.stopFaceDetection();
                Log.d(TAG, "Face detection cleaned up");
            } catch (Exception e) {
                Log.e(TAG, "Error cleaning up face detection", e);
            }
        }

        // Cleanup smart greeting
        if (smartGreeting != null) {
            try {
                smartGreeting.cleanup();
                Log.d(TAG, "Smart greeting cleaned up");
            } catch (Exception e) {
                Log.e(TAG, "Error cleaning up smart greeting", e);
            }
        }

        Log.d(TAG, "MainActivity cleanup completed");
    }

    @Override
    protected void onPause() {
        super.onPause();
        // Pause face detection to save battery
        if (faceDetection != null) {
            faceDetection.pauseFaceDetection();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        // Resume face detection
        if (faceDetection != null) {
            faceDetection.resumeFaceDetection();
        }
    }
    
    /**
     * ViewPager2 adapter for tab fragments
     */
    private class TabPagerAdapter extends FragmentStateAdapter {
        public TabPagerAdapter(@NonNull FragmentActivity fragmentActivity) {
            super(fragmentActivity);
        }
        
        @NonNull
        @Override
        public Fragment createFragment(int position) {
            switch (position) {
                case 0:
                    return controlFragment;
                case 1:
                    return aiFragment;
                case 2:
                    return presetFragment;
                case 3:
                    return settingsFragment;
                default:
                    return controlFragment;
            }
        }

        @Override
        public int getItemCount() {
            return 4; // Control, AI, Settings
        }
    }
}
