@echo off
echo ========================================
echo STEM Simple Robot - Build and Test
echo ========================================
echo.

echo [1/5] Checking Gradle Wrapper...
if not exist gradlew.bat (
    echo ERROR: Gradle wrapper not found!
    echo Please run this script from the project root directory.
    pause
    exit /b 1
)

echo [2/5] Cleaning previous build...
call gradlew clean
if %ERRORLEVEL% neq 0 (
    echo ERROR: Clean failed!
    pause
    exit /b 1
)

echo [3/5] Building debug APK...
call gradlew assembleDebug
if %ERRORLEVEL% neq 0 (
    echo ERROR: Build failed!
    echo.
    echo Common issues:
    echo - Check Android SDK is installed
    echo - Verify ANDROID_HOME environment variable
    echo - Ensure Java 8+ is installed
    echo - Check internet connection for dependencies
    pause
    exit /b 1
)

echo [4/5] Running lint checks...
call gradlew lint
if %ERRORLEVEL% neq 0 (
    echo WARNING: Lint checks found issues
    echo Check app/build/reports/lint-results.html for details
)

echo [5/5] Build completed successfully!
echo.
echo APK Location: app\build\outputs\apk\debug\app-debug.apk
echo.
echo Next steps:
echo 1. Install APK on Android device
echo 2. Upload ESP32 firmware (esp32-simple-controller.ino)
echo 3. Configure robot hardware connections
echo 4. Test all features using the app
echo.

echo ========================================
echo Build Summary
echo ========================================
echo Status: SUCCESS
echo APK: app-debug.apk
echo Size: 
for %%A in (app\build\outputs\apk\debug\app-debug.apk) do echo %%~zA bytes
echo.

echo Would you like to open the APK location? (y/n)
set /p choice=
if /i "%choice%"=="y" (
    explorer app\build\outputs\apk\debug\
)

pause
