package com.stemrobo.simple.robot;

import android.content.Context;
import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbDeviceConnection;
import android.hardware.usb.UsbManager;
import android.util.Log;
import com.hoho.android.usbserial.driver.UsbSerialDriver;
import com.hoho.android.usbserial.driver.UsbSerialPort;
import com.hoho.android.usbserial.driver.UsbSerialProber;
import okhttp3.*;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Enhanced ESP32 Communication Manager
 * Supports USB Serial (primary) and WiFi HTTP communication
 * Automatically switches between communication modes
 */
public class ESP32EnhancedComm {
    private static final String TAG = "ESP32EnhancedComm";
    
    // Communication modes
    public enum CommMode {
        USB_SERIAL,    // Primary mode
        WIFI_HTTP      // Fallback mode
    }
    
    // USB Serial settings
    private static final int BAUD_RATE = 115200;
    private static final int DATA_BITS = 8;
    private static final int STOP_BITS = UsbSerialPort.STOPBITS_1;
    private static final int PARITY = UsbSerialPort.PARITY_NONE;
    private static final int USB_TIMEOUT = 2000;
    
    // WiFi settings
    private static final int ESP32_PORT = 80;
    private static final int CONNECTION_TIMEOUT = 5; // seconds
    private static final int READ_TIMEOUT = 10; // seconds
    
    // Communication components
    private Context context;
    private CommMode currentMode = CommMode.USB_SERIAL;
    private boolean isConnected = false;
    
    // USB Serial components
    private UsbManager usbManager;
    private UsbSerialPort usbSerialPort;
    private UsbDeviceConnection usbConnection;
    
    // WiFi components
    private String esp32IP = "***********";
    private OkHttpClient httpClient;
    
    // Threading
    private ExecutorService executorService;
    
    // Callbacks
    private ConnectionCallback connectionCallback;
    private DistanceCallback distanceCallback;
    
    public interface ConnectionCallback {
        void onConnectionStatusChanged(boolean connected, String message, CommMode mode);
    }
    
    public interface DistanceCallback {
        void onDistanceReceived(float distance);
        void onDistanceError(String error);
    }
    
    public ESP32EnhancedComm(Context context) {
        this.context = context;
        this.executorService = Executors.newCachedThreadPool();
        initializeComponents();
    }
    
    private void initializeComponents() {
        // Initialize USB manager
        usbManager = (UsbManager) context.getSystemService(Context.USB_SERVICE);
        
        // Initialize HTTP client for WiFi fallback
        httpClient = new OkHttpClient.Builder()
            .connectTimeout(CONNECTION_TIMEOUT, TimeUnit.SECONDS)
            .readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
            .writeTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
            .build();
            
        Log.d(TAG, "Enhanced ESP32 communication initialized");
    }
    
    public void setConnectionCallback(ConnectionCallback callback) {
        this.connectionCallback = callback;
    }
    
    public void setDistanceCallback(DistanceCallback callback) {
        this.distanceCallback = callback;
    }
    
    public void setWiFiIP(String ip) {
        this.esp32IP = ip;
        Log.d(TAG, "WiFi IP updated to: " + ip);
    }
    
    // ===== CONNECTION MANAGEMENT =====
    
    public void connect() {
        executorService.execute(() -> {
            // Try USB first (primary mode)
            if (connectUSB()) {
                currentMode = CommMode.USB_SERIAL;
                isConnected = true;
                notifyConnectionStatus(true, "Connected via USB Serial", currentMode);
                return;
            }
            
            // Fallback to WiFi
            if (connectWiFi()) {
                currentMode = CommMode.WIFI_HTTP;
                isConnected = true;
                notifyConnectionStatus(true, "Connected via WiFi", currentMode);
                return;
            }
            
            // Both failed
            isConnected = false;
            notifyConnectionStatus(false, "Connection failed - check USB cable and WiFi", CommMode.USB_SERIAL);
        });
    }
    
    private boolean connectUSB() {
        try {
            List<UsbSerialDriver> availableDrivers = UsbSerialProber.getDefaultProber().findAllDrivers(usbManager);
            
            if (availableDrivers.isEmpty()) {
                Log.d(TAG, "No USB serial devices found");
                return false;
            }
            
            UsbSerialDriver driver = availableDrivers.get(0);
            UsbDevice device = driver.getDevice();
            
            usbConnection = usbManager.openDevice(device);
            if (usbConnection == null) {
                Log.e(TAG, "Failed to open USB device connection");
                return false;
            }
            
            usbSerialPort = driver.getPorts().get(0);
            usbSerialPort.open(usbConnection);
            usbSerialPort.setParameters(BAUD_RATE, DATA_BITS, STOP_BITS, PARITY);
            
            Log.d(TAG, "USB Serial connected successfully");
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "USB connection failed", e);
            return false;
        }
    }
    
    private boolean connectWiFi() {
        try {
            String url = "http://" + esp32IP + ":" + ESP32_PORT + "/api/status";
            Request request = new Request.Builder().url(url).build();
            
            Response response = httpClient.newCall(request).execute();
            boolean success = response.isSuccessful();
            response.close();
            
            Log.d(TAG, "WiFi connection test: " + (success ? "success" : "failed"));
            return success;
            
        } catch (Exception e) {
            Log.e(TAG, "WiFi connection failed", e);
            return false;
        }
    }
    
    public void disconnect() {
        isConnected = false;
        
        if (usbSerialPort != null) {
            try {
                usbSerialPort.close();
                usbSerialPort = null;
            } catch (Exception e) {
                Log.e(TAG, "Error closing USB serial port", e);
            }
        }
        
        if (usbConnection != null) {
            usbConnection.close();
            usbConnection = null;
        }
        
        notifyConnectionStatus(false, "Disconnected", currentMode);
        Log.d(TAG, "Disconnected from ESP32");
    }
    
    // ===== COMMAND SENDING =====
    
    public void sendCommand(String command) {
        if (!isConnected) {
            Log.w(TAG, "Not connected - cannot send command: " + command);
            return;
        }
        
        executorService.execute(() -> {
            try {
                if (currentMode == CommMode.USB_SERIAL) {
                    sendUSBCommand(command);
                } else {
                    sendWiFiCommand(command);
                }
            } catch (Exception e) {
                Log.e(TAG, "Error sending command: " + command, e);
                // Try to reconnect
                connect();
            }
        });
    }
    
    private void sendUSBCommand(String command) throws IOException {
        if (usbSerialPort == null) {
            throw new IOException("USB serial port not available");
        }
        
        String fullCommand = command + "\n";
        byte[] data = fullCommand.getBytes();
        
        usbSerialPort.write(data, USB_TIMEOUT);
        // Note: write method returns void, so we assume success if no exception is thrown
        
        Log.d(TAG, "USB command sent: " + command);
        
        // Read response if available
        readUSBResponse();
    }
    
    private void readUSBResponse() {
        try {
            byte[] buffer = new byte[256];
            int bytesRead = usbSerialPort.read(buffer, USB_TIMEOUT);
            
            if (bytesRead > 0) {
                String response = new String(buffer, 0, bytesRead).trim();
                Log.d(TAG, "USB response: " + response);
                
                // Parse distance responses
                if (response.startsWith("DISTANCE:")) {
                    try {
                        float distance = Float.parseFloat(response.substring(9));
                        if (distanceCallback != null) {
                            distanceCallback.onDistanceReceived(distance);
                        }
                    } catch (NumberFormatException e) {
                        Log.e(TAG, "Invalid distance format: " + response);
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error reading USB response", e);
        }
    }
    
    private void sendWiFiCommand(String command) throws IOException {
        String url = "http://" + esp32IP + ":" + ESP32_PORT + "/api/command";
        
        RequestBody body = new FormBody.Builder()
            .add("command", command)
            .build();
            
        Request request = new Request.Builder()
            .url(url)
            .post(body)
            .build();
            
        Response response = httpClient.newCall(request).execute();
        
        if (response.isSuccessful()) {
            Log.d(TAG, "WiFi command sent: " + command);
        } else {
            throw new IOException("WiFi command failed: " + response.code());
        }
        
        response.close();
    }
    
    // ===== MOVEMENT COMMANDS =====
    
    public void moveForward() {
        sendCommand("MOVE_FORWARD");
    }
    
    public void moveBackward() {
        sendCommand("MOVE_BACKWARD");
    }
    
    public void turnLeft() {
        sendCommand("TURN_LEFT");
    }
    
    public void turnRight() {
        sendCommand("TURN_RIGHT");
    }
    
    public void stopMovement() {
        sendCommand("STOP");
    }
    
    // ===== SERVO COMMANDS =====
    
    public void setLeftArmPosition(int position) {
        sendCommand("ARM_LEFT_" + position);
    }
    
    public void setRightArmPosition(int position) {
        sendCommand("ARM_RIGHT_" + position);
    }
    
    public void setHeadPosition(int position) {
        sendCommand("HEAD_" + position);
    }
    
    // ===== GESTURE COMMANDS =====
    
    public void performWave() {
        sendCommand("WAVE");
    }
    
    public void performPoint() {
        sendCommand("POINT");
    }
    
    public void performHandshake() {
        sendCommand("HANDSHAKE");
    }
    
    public void moveToRestPosition() {
        sendCommand("REST");
    }
    
    public void lookLeft() {
        sendCommand("LOOK_LEFT");
    }
    
    public void lookRight() {
        sendCommand("LOOK_RIGHT");
    }
    
    public void lookCenter() {
        sendCommand("LOOK_CENTER");
    }
    
    // ===== SENSOR COMMANDS =====
    
    public void requestDistance() {
        sendCommand("GET_DISTANCE");
    }
    
    public void requestStatus() {
        sendCommand("GET_STATUS");
    }
    
    // ===== UTILITY METHODS =====
    
    public boolean isConnected() {
        return isConnected;
    }
    
    public CommMode getCurrentMode() {
        return currentMode;
    }
    
    private void notifyConnectionStatus(boolean connected, String message, CommMode mode) {
        if (connectionCallback != null) {
            connectionCallback.onConnectionStatusChanged(connected, message, mode);
        }
    }
    
    public void cleanup() {
        disconnect();
        if (executorService != null) {
            executorService.shutdown();
        }
        Log.d(TAG, "ESP32 Enhanced Communication cleaned up");
    }
}
