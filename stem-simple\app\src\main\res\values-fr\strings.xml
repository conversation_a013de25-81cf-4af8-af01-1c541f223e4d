<resources>
    <string name="app_name">Robot STEM Simple</string>
    
    <!-- Tab titles -->
    <string name="tab_control">Contr<PERSON><PERSON></string>
    <string name="tab_ai">Assistant IA</string>
    <string name="tab_settings">Paramètres</string>
    
    <!-- Control tab -->
    <string name="movement_controls">Contrôles de Mouvement</string>
    <string name="servo_controls">Contrôles de Servo</string>
    <string name="sensor_status">État du Capteur</string>
    <string name="forward">Avant</string>
    <string name="backward">Arrière</string>
    <string name="left">Gauche</string>
    <string name="right">Droite</string>
    <string name="stop">Arr<PERSON><PERSON></string>
    <string name="wave">Saluer</string>
    <string name="point">Pointer</string>
    <string name="rest">Repos</string>
    <string name="distance">Distance: %1$s cm</string>
    <string name="face_count">Visages: %1$d</string>
    
    <!-- AI tab -->
    <string name="ai_assistant">Assistant IA</string>
    <string name="speak_command">Parlez votre commande</string>
    <string name="type_command">Tapez votre commande</string>
    <string name="send">Envoyer</string>
    <string name="listening">Écoute...</string>
    <string name="processing">Traitement...</string>
    <string name="preset_created">Préréglage créé: %1$s</string>
    <string name="preset_executed">Exécution du préréglage: %1$s</string>
    
    <!-- Settings tab -->
    <string name="robot_settings">Paramètres du Robot</string>
    <string name="ai_settings">Paramètres IA</string>
    <string name="greeting_settings">Paramètres de Salutation Intelligente</string>
    <string name="esp32_ip">Adresse IP ESP32</string>
    <string name="greeting_enabled">Activer la Salutation Intelligente</string>
    <string name="greeting_distance">Distance de Salutation (cm)</string>
    <string name="greeting_cooldown">Délai de Salutation (secondes)</string>
    <string name="ai_wake_word">Mot de Réveil IA</string>
    <string name="language">Langue</string>
    <string name="voice_gender">Genre de Voix</string>
    <string name="male">Masculin</string>
    <string name="female">Féminin</string>
    
    <!-- Status messages -->
    <string name="robot_online">Robot En Ligne</string>
    <string name="robot_offline">Robot Hors Ligne</string>
    <string name="connecting">Connexion...</string>
    <string name="connection_failed">Connexion Échouée</string>
    <string name="camera_permission_required">Permission de caméra requise</string>
    <string name="microphone_permission_required">Permission de microphone requise</string>
    
    <!-- Error messages -->
    <string name="error_camera_init">Échec d\'initialisation de la caméra</string>
    <string name="error_ai_service">Service IA indisponible</string>
    <string name="error_robot_communication">Erreur de communication du robot</string>
    <string name="error_invalid_command">Commande invalide</string>
</resources>
