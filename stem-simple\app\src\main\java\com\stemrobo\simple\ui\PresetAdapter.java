package com.stemrobo.simple.ui;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.stemrobo.simple.R;
import com.stemrobo.simple.ai.RobotPreset;
import java.util.List;

/**
 * Preset Adapter for displaying and executing robot presets
 */
public class PresetAdapter extends RecyclerView.Adapter<PresetAdapter.PresetViewHolder> {
    
    private final List<RobotPreset> presets;
    private final OnPresetClickListener clickListener;
    
    public interface OnPresetClickListener {
        void onPresetClick(RobotPreset preset);
    }
    
    public PresetAdapter(List<RobotPreset> presets, OnPresetClickListener clickListener) {
        this.presets = presets;
        this.clickListener = clickListener;
    }
    
    @NonNull
    @Override
    public PresetViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
            .inflate(R.layout.item_preset, parent, false);
        return new PresetViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull PresetViewHolder holder, int position) {
        RobotPreset preset = presets.get(position);
        holder.bind(preset, clickListener);
    }
    
    @Override
    public int getItemCount() {
        return presets.size();
    }
    
    static class PresetViewHolder extends RecyclerView.ViewHolder {
        private final TextView presetName;
        private final TextView presetDescription;
        private final TextView commandCount;
        private final Button executeButton;
        
        public PresetViewHolder(@NonNull View itemView) {
            super(itemView);
            presetName = itemView.findViewById(R.id.preset_name);
            presetDescription = itemView.findViewById(R.id.preset_description);
            commandCount = itemView.findViewById(R.id.command_count);
            executeButton = itemView.findViewById(R.id.execute_button);
        }
        
        public void bind(RobotPreset preset, OnPresetClickListener clickListener) {
            presetName.setText(preset.getName());
            presetDescription.setText(preset.getDescription());
            commandCount.setText(preset.getCommandCount() + " commands");
            
            executeButton.setOnClickListener(v -> {
                if (clickListener != null) {
                    clickListener.onPresetClick(preset);
                }
            });
            
            // Set click listener for the entire item
            itemView.setOnClickListener(v -> {
                if (clickListener != null) {
                    clickListener.onPresetClick(preset);
                }
            });
        }
    }
}
