<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 STEM-Xpert Robot Chatbot</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 90vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }

        .chat-header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .chat-header p {
            font-size: 14px;
            opacity: 0.9;
        }

        .status-indicator {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 12px;
            height: 12px;
            background: #ff4444;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .status-indicator.connected {
            background: #44ff44;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
        }

        .message.bot .message-content {
            background: #e3f2fd;
            color: #1976d2;
            border-bottom-left-radius: 4px;
        }

        .message.user .message-content {
            background: #4CAF50;
            color: white;
            border-bottom-right-radius: 4px;
        }

        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            margin: 0 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }

        .message.bot .message-avatar {
            background: #2196F3;
            color: white;
        }

        .message.user .message-avatar {
            background: #4CAF50;
            color: white;
        }

        .typing-indicator {
            display: none;
            padding: 12px 16px;
            background: #e3f2fd;
            border-radius: 18px;
            border-bottom-left-radius: 4px;
            max-width: 70%;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dots span {
            width: 8px;
            height: 8px;
            background: #1976d2;
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }

        .typing-dots span:nth-child(2) { animation-delay: 0.2s; }
        .typing-dots span:nth-child(3) { animation-delay: 0.4s; }

        @keyframes typing {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-10px); }
        }

        .chat-input {
            padding: 20px;
            background: white;
            border-top: 1px solid #e0e0e0;
        }

        .input-container {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .message-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }

        .message-input:focus {
            border-color: #4CAF50;
        }

        .input-button {
            width: 48px;
            height: 48px;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            transition: all 0.3s;
        }

        .send-button {
            background: #4CAF50;
            color: white;
        }

        .send-button:hover {
            background: #45a049;
            transform: scale(1.05);
        }

        .mic-button {
            background: #2196F3;
            color: white;
        }

        .mic-button:hover {
            background: #1976d2;
            transform: scale(1.05);
        }

        .mic-button.recording {
            background: #f44336;
            animation: pulse 1s infinite;
        }

        .error-message {
            background: #ffebee;
            color: #c62828;
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #f44336;
        }

        .welcome-message {
            text-align: center;
            color: #666;
            padding: 40px 20px;
            font-style: italic;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .chat-container {
                width: 95%;
                height: 95vh;
                border-radius: 10px;
            }
            
            .chat-header h1 {
                font-size: 20px;
            }
            
            .message-content {
                max-width: 85%;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <h1>🤖 STEM-Xpert Robot Assistant</h1>
            <p>Your AI-powered robotics learning companion</p>
            <div class="status-indicator" id="statusIndicator"></div>
        </div>

        <div class="chat-messages" id="chatMessages">
            <div class="welcome-message">
                <h3>👋 Welcome to STEM-Xpert Robot Chatbot!</h3>
                <p>I'm here to help you learn about robotics, programming, and STEM concepts.<br>
                Ask me anything about your humanoid robot project!</p>
            </div>
        </div>

        <div class="chat-input">
            <div class="input-container">
                <input type="text" id="messageInput" class="message-input" 
                       placeholder="Ask me about robotics, programming, or your robot..." 
                       maxlength="500">
                <button id="micButton" class="input-button mic-button" title="Voice Input">🎤</button>
                <button id="sendButton" class="input-button send-button" title="Send Message">📤</button>
            </div>
            <div id="errorContainer"></div>
        </div>
    </div>

    <script>
        class RobotChatbot {
            constructor() {
                this.apiKey = ''; // Students will add their Gemini API key here
                this.apiUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent';
                this.isRecording = false;
                this.recognition = null;
                
                this.initializeElements();
                this.initializeEventListeners();
                this.initializeSpeechRecognition();
                this.updateConnectionStatus();
            }

            initializeElements() {
                this.chatMessages = document.getElementById('chatMessages');
                this.messageInput = document.getElementById('messageInput');
                this.sendButton = document.getElementById('sendButton');
                this.micButton = document.getElementById('micButton');
                this.statusIndicator = document.getElementById('statusIndicator');
                this.errorContainer = document.getElementById('errorContainer');
            }

            initializeEventListeners() {
                this.sendButton.addEventListener('click', () => this.sendMessage());
                this.micButton.addEventListener('click', () => this.toggleVoiceRecording());
                
                this.messageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });

                this.messageInput.addEventListener('input', () => {
                    this.clearError();
                });
            }

            initializeSpeechRecognition() {
                if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                    this.recognition = new SpeechRecognition();
                    
                    this.recognition.continuous = false;
                    this.recognition.interimResults = false;
                    this.recognition.lang = 'en-US';

                    this.recognition.onstart = () => {
                        this.isRecording = true;
                        this.micButton.classList.add('recording');
                        this.micButton.innerHTML = '🔴';
                        this.showMessage('🎤 Listening... Speak now!', 'bot');
                    };

                    this.recognition.onresult = (event) => {
                        const transcript = event.results[0][0].transcript;
                        this.messageInput.value = transcript;
                        this.removeLastBotMessage(); // Remove "Listening..." message
                        this.showMessage(`🎤 "${transcript}"`, 'user');
                        this.sendMessage();
                    };

                    this.recognition.onerror = (event) => {
                        this.showError(`Voice recognition error: ${event.error}`);
                        this.stopRecording();
                    };

                    this.recognition.onend = () => {
                        this.stopRecording();
                    };
                } else {
                    this.micButton.style.display = 'none';
                    console.warn('Speech recognition not supported in this browser');
                }
            }

            updateConnectionStatus() {
                if (this.apiKey) {
                    this.statusIndicator.classList.add('connected');
                    this.statusIndicator.title = 'Connected to Gemini AI';
                } else {
                    this.statusIndicator.classList.remove('connected');
                    this.statusIndicator.title = 'API Key Required';
                    this.showError('Please add your Gemini API key to enable AI responses. Get one from: https://makersuite.google.com/app/apikey');
                }
            }

            async sendMessage() {
                const message = this.messageInput.value.trim();
                if (!message) return;

                // Clear input and show user message
                this.messageInput.value = '';
                this.showMessage(message, 'user');

                // Show typing indicator
                this.showTypingIndicator();

                try {
                    if (!this.apiKey) {
                        throw new Error('API key not configured. Please add your Gemini API key.');
                    }

                    const response = await this.callGeminiAPI(message);
                    this.hideTypingIndicator();
                    this.showMessage(response, 'bot');
                    
                } catch (error) {
                    this.hideTypingIndicator();
                    this.showError(`Error: ${error.message}`);
                    this.showMessage("I'm having trouble connecting right now. Please check your API key and try again.", 'bot');
                }
            }

            async callGeminiAPI(message) {
                const systemPrompt = `You are a friendly AI robot assistant created by STEM-Xpert for educational robotics workshops. 
                You help students learn about robotics, programming, electronics, and STEM concepts. 
                Keep responses educational, encouraging, and age-appropriate for school students (12-18 years). 
                Be enthusiastic about robotics and learning. If asked about robotics components, programming, or workshop activities, provide helpful explanations.
                Keep responses concise but informative.`;

                const requestBody = {
                    contents: [{
                        parts: [{
                            text: `${systemPrompt}\n\nStudent question: ${message}`
                        }]
                    }]
                };

                const response = await fetch(`${this.apiUrl}?key=${this.apiKey}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody)
                });

                if (!response.ok) {
                    throw new Error(`API request failed: ${response.status} ${response.statusText}`);
                }

                const data = await response.json();
                
                if (data.candidates && data.candidates[0] && data.candidates[0].content) {
                    return data.candidates[0].content.parts[0].text;
                } else {
                    throw new Error('Invalid response format from API');
                }
            }

            showMessage(message, sender) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${sender}`;
                
                const avatar = document.createElement('div');
                avatar.className = 'message-avatar';
                avatar.textContent = sender === 'bot' ? '🤖' : '👤';
                
                const content = document.createElement('div');
                content.className = 'message-content';
                content.textContent = message;
                
                if (sender === 'bot') {
                    messageDiv.appendChild(avatar);
                    messageDiv.appendChild(content);
                } else {
                    messageDiv.appendChild(content);
                    messageDiv.appendChild(avatar);
                }
                
                this.chatMessages.appendChild(messageDiv);
                this.scrollToBottom();
            }

            showTypingIndicator() {
                const typingDiv = document.createElement('div');
                typingDiv.className = 'message bot';
                typingDiv.id = 'typingIndicator';
                
                const avatar = document.createElement('div');
                avatar.className = 'message-avatar';
                avatar.textContent = '🤖';
                
                const typingContent = document.createElement('div');
                typingContent.className = 'typing-indicator';
                typingContent.style.display = 'block';
                typingContent.innerHTML = '<div class="typing-dots"><span></span><span></span><span></span></div>';
                
                typingDiv.appendChild(avatar);
                typingDiv.appendChild(typingContent);
                this.chatMessages.appendChild(typingDiv);
                this.scrollToBottom();
            }

            hideTypingIndicator() {
                const typingIndicator = document.getElementById('typingIndicator');
                if (typingIndicator) {
                    typingIndicator.remove();
                }
            }

            removeLastBotMessage() {
                const messages = this.chatMessages.querySelectorAll('.message.bot');
                if (messages.length > 0) {
                    const lastMessage = messages[messages.length - 1];
                    if (lastMessage.textContent.includes('Listening...')) {
                        lastMessage.remove();
                    }
                }
            }

            toggleVoiceRecording() {
                if (!this.recognition) {
                    this.showError('Voice recognition not supported in this browser');
                    return;
                }

                if (this.isRecording) {
                    this.recognition.stop();
                } else {
                    this.recognition.start();
                }
            }

            stopRecording() {
                this.isRecording = false;
                this.micButton.classList.remove('recording');
                this.micButton.innerHTML = '🎤';
            }

            showError(message) {
                this.errorContainer.innerHTML = `<div class="error-message">${message}</div>`;
                setTimeout(() => this.clearError(), 5000);
            }

            clearError() {
                this.errorContainer.innerHTML = '';
            }

            scrollToBottom() {
                this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
            }

            // Method for students to set their API key
            setApiKey(key) {
                this.apiKey = key;
                this.updateConnectionStatus();
                this.clearError();
                this.showMessage("Great! API key configured successfully. I'm ready to help you with your robotics questions! 🚀", 'bot');
            }
        }

        // Initialize the chatbot
        const chatbot = new RobotChatbot();

        // Function for students to easily set their API key
        function setGeminiApiKey(apiKey) {
            chatbot.setApiKey(apiKey);
        }

        // Show instructions for setting up API key
        console.log('🤖 STEM-Xpert Robot Chatbot Ready!');
        console.log('📝 To enable AI responses, get your free Gemini API key from: https://makersuite.google.com/app/apikey');
        console.log('🔑 Then run: setGeminiApiKey("YOUR_API_KEY_HERE")');
    </script>
</body>
</html>
