<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal"
    android:background="@color/background_primary"
    android:padding="4dp">

    <!-- Left Panel: Chat and Controls -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:orientation="vertical"
        android:layout_marginEnd="8dp">

        <!-- AI Chat Section -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:layout_marginBottom="8dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:padding="8dp">

                <!-- Chat Header with Status -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="AI"
                        android:textSize="12sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary" />

                    <!-- Emergency Stop Button -->
                    <Button
                        android:id="@+id/btn_emergency_stop"
                        android:layout_width="wrap_content"
                        android:layout_height="32dp"
                        android:layout_marginEnd="8dp"
                        android:text="🛑 STOP"
                        android:textSize="12sp"
                        android:background="@color/button_danger"
                        android:textColor="@android:color/white"
                        android:minWidth="0dp"
                        android:paddingStart="8dp"
                        android:paddingEnd="8dp" />

                    <!-- Listening Indicator -->
                    <View
                        android:id="@+id/listening_indicator"
                        android:layout_width="12dp"
                        android:layout_height="12dp"
                        android:layout_marginEnd="8dp"
                        android:background="@drawable/circle_shape"
                        android:backgroundTint="@color/indicator_inactive" />

                    <TextView
                        android:id="@+id/ai_status"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Ready to assist"
                        android:textColor="@color/text_secondary"
                        android:textSize="12sp" />

                </LinearLayout>

                <!-- Chat Messages -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/chat_recycler_view"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:scrollbars="vertical" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Input Controls -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="12dp">

                <!-- Input Row -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <EditText
                        android:id="@+id/command_input"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:hint="Type command or use voice..."
                        android:background="@drawable/edit_text_background"
                        android:padding="8dp"
                        android:textSize="14sp" />

                    <Button
                        android:id="@+id/btn_send_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:text="Send"
                        android:textSize="12sp"
                        android:background="@color/button_primary"
                        android:textColor="@android:color/white"
                        android:minWidth="0dp" />

                </LinearLayout>

                <!-- Voice and Control Buttons -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginTop="8dp">

                    <Button
                        android:id="@+id/btn_voice_input"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="🎤 Voice"
                        android:textSize="12sp"
                        android:background="@color/button_primary"
                        android:textColor="@android:color/white"
                        android:layout_marginEnd="4dp" />

                    <Button
                        android:id="@+id/btn_clear_chat"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Clear"
                        android:textSize="12sp"
                        android:background="@color/button_secondary"
                        android:textColor="@android:color/white"
                        android:layout_marginStart="4dp" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

    <!-- Right Panel: Settings -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:orientation="vertical"
        android:layout_marginStart="8dp">

        <!-- Voice Settings -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="12dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Voice Settings"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="8dp" />

                <!-- Auto Voice Switch -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Auto Voice"
                        android:textSize="12sp"
                        android:textColor="@color/text_secondary" />

                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/switch_auto_voice"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                </LinearLayout>

                <!-- AI Language Spinner -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Language"
                        android:textSize="12sp"
                        android:textColor="@color/text_secondary" />

                    <Spinner
                        android:id="@+id/spinner_ai_language"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:minWidth="100dp" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

</LinearLayout>
