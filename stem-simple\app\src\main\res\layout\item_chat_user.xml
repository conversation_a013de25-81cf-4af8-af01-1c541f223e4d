<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="8dp"
    android:gravity="end">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical"
        android:layout_marginStart="64dp"
        android:layout_marginEnd="8dp">

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            app:cardBackgroundColor="@color/primary_blue"
            app:cardCornerRadius="16dp"
            app:cardElevation="2dp"
            xmlns:app="http://schemas.android.com/apk/res-auto">

            <TextView
                android:id="@+id/message_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="12dp"
                android:text="User message"
                android:textColor="@color/white"
                android:textSize="16sp" />

        </com.google.android.material.card.MaterialCardView>

        <TextView
            android:id="@+id/time_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_marginTop="4dp"
            android:text="12:34"
            android:textColor="@color/text_secondary"
            android:textSize="12sp" />

    </LinearLayout>

</LinearLayout>
