<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary">

    <!-- Main Content -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header Section -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="16dp"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Robot Presets"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary" />

                <Button
                    android:id="@+id/btn_refresh_presets"
                    android:layout_width="wrap_content"
                    android:layout_height="36dp"
                    android:text="🔄 Refresh"
                    android:textSize="12sp"
                    android:background="@color/button_secondary"
                    android:textColor="@android:color/white"
                    android:minWidth="0dp"
                    android:paddingStart="12dp"
                    android:paddingEnd="12dp" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Presets List -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <!-- List Header -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:background="@color/surface_light"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Name"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary" />

                    <TextView
                        android:layout_width="80dp"
                        android:layout_height="wrap_content"
                        android:text="Actions"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:gravity="center" />

                    <TextView
                        android:layout_width="60dp"
                        android:layout_height="wrap_content"
                        android:text="Steps"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:gravity="center" />

                </LinearLayout>

                <!-- Divider -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/text_hint" />

                <!-- Presets RecyclerView -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/presets_recycler_view"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:scrollbars="vertical"
                    android:clipToPadding="false"
                    android:paddingBottom="80dp" />

                <!-- Empty State -->
                <LinearLayout
                    android:id="@+id/empty_state"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="32dp"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="🤖"
                        android:textSize="48sp"
                        android:layout_marginBottom="16dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="No presets created yet"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Tap the + button to create your first preset"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary"
                        android:gravity="center" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

    <!-- Floating Action Button -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_create_preset"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:src="@drawable/ic_add"
        android:contentDescription="Create new preset"
        app:tint="@android:color/white"
        app:backgroundTint="@color/primary_blue" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
