<resources>
    <string name="app_name">STEM Einfacher Roboter</string>
    
    <!-- Tab titles -->
    <string name="tab_control">Steuerung</string>
    <string name="tab_ai">KI-Assistent</string>
    <string name="tab_settings">Einstellungen</string>
    
    <!-- Control tab -->
    <string name="movement_controls">Bewegungssteuerung</string>
    <string name="servo_controls">Servo-Steuerung</string>
    <string name="sensor_status">Sensor-Status</string>
    <string name="forward">Vorwärts</string>
    <string name="backward">Rückwärts</string>
    <string name="left">Links</string>
    <string name="right">Rechts</string>
    <string name="stop">Stopp</string>
    <string name="wave">Winken</string>
    <string name="point">Zeigen</string>
    <string name="rest">Ruhe</string>
    <string name="distance">Entfernung: %1$s cm</string>
    <string name="face_count">G<PERSON>chter: %1$d</string>
    
    <!-- AI tab -->
    <string name="ai_assistant">KI-Assistent</string>
    <string name="speak_command">Sprechen Sie Ihren Befehl</string>
    <string name="type_command">Geben Sie Ihren Befehl ein</string>
    <string name="send">Senden</string>
    <string name="listening">Zuhören...</string>
    <string name="processing">Verarbeitung...</string>
    <string name="preset_created">Voreinstellung erstellt: %1$s</string>
    <string name="preset_executed">Voreinstellung ausführen: %1$s</string>
    
    <!-- Settings tab -->
    <string name="robot_settings">Roboter-Einstellungen</string>
    <string name="ai_settings">KI-Einstellungen</string>
    <string name="greeting_settings">Intelligente Begrüßungseinstellungen</string>
    <string name="esp32_ip">ESP32 IP-Adresse</string>
    <string name="greeting_enabled">Intelligente Begrüßung aktivieren</string>
    <string name="greeting_distance">Begrüßungsentfernung (cm)</string>
    <string name="greeting_cooldown">Begrüßungs-Abklingzeit (Sekunden)</string>
    <string name="ai_wake_word">KI-Aktivierungswort</string>
    <string name="language">Sprache</string>
    <string name="voice_gender">Stimmengeschlecht</string>
    <string name="male">Männlich</string>
    <string name="female">Weiblich</string>
    
    <!-- Status messages -->
    <string name="robot_online">Roboter Online</string>
    <string name="robot_offline">Roboter Offline</string>
    <string name="connecting">Verbinden...</string>
    <string name="connection_failed">Verbindung fehlgeschlagen</string>
    <string name="camera_permission_required">Kamera-Berechtigung erforderlich</string>
    <string name="microphone_permission_required">Mikrofon-Berechtigung erforderlich</string>
    
    <!-- Error messages -->
    <string name="error_camera_init">Kamera-Initialisierung fehlgeschlagen</string>
    <string name="error_ai_service">KI-Service nicht verfügbar</string>
    <string name="error_robot_communication">Roboter-Kommunikationsfehler</string>
    <string name="error_invalid_command">Ungültiger Befehl</string>
</resources>
