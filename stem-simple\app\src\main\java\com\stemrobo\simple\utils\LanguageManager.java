package com.stemrobo.simple.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Build;
import android.util.Log;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * Language Manager for STEM Simple Robot
 * Handles multi-language support and locale management
 */
public class LanguageManager {
    private static final String TAG = "LanguageManager";
    private static final String PREFS_NAME = "language_settings";
    private static final String KEY_SELECTED_LANGUAGE = "selected_language";
    private static final String DEFAULT_LANGUAGE = "en";

    // Language constants
    public static final String LANG_ENGLISH = "en";
    public static final String LANG_MALAYALAM = "ml";
    public static final String LANG_HINDI = "hi";
    public static final String LANG_ARABIC = "ar";
    
    // Supported languages
    public static class Language {
        public final String code;
        public final String name;
        public final String nativeName;
        
        public Language(String code, String name, String nativeName) {
            this.code = code;
            this.name = name;
            this.nativeName = nativeName;
        }
        
        @Override
        public String toString() {
            return nativeName + " (" + name + ")";
        }
    }
    
    private static final List<Language> SUPPORTED_LANGUAGES = new ArrayList<>();
    
    static {
        SUPPORTED_LANGUAGES.add(new Language("en", "English", "English"));
        SUPPORTED_LANGUAGES.add(new Language("ml", "Malayalam", "മലയാളം"));
        SUPPORTED_LANGUAGES.add(new Language("hi", "Hindi", "हिन्दी"));
        SUPPORTED_LANGUAGES.add(new Language("ar", "Arabic", "العربية"));
    }
    
    private final Context context;
    private final SharedPreferences preferences;
    
    public LanguageManager(Context context) {
        this.context = context.getApplicationContext();
        this.preferences = this.context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
    }
    
    /**
     * Get list of supported languages
     */
    public static List<Language> getSupportedLanguages() {
        return new ArrayList<>(SUPPORTED_LANGUAGES);
    }
    
    /**
     * Get currently selected language
     */
    public String getCurrentLanguage() {
        return preferences.getString(KEY_SELECTED_LANGUAGE, DEFAULT_LANGUAGE);
    }
    
    /**
     * Get current language object
     */
    public Language getCurrentLanguageObject() {
        String currentCode = getCurrentLanguage();
        for (Language language : SUPPORTED_LANGUAGES) {
            if (language.code.equals(currentCode)) {
                return language;
            }
        }
        return SUPPORTED_LANGUAGES.get(0); // Default to English
    }
    
    /**
     * Set application language
     */
    public void setLanguage(String languageCode) {
        if (!isLanguageSupported(languageCode)) {
            Log.w(TAG, "Language not supported: " + languageCode + ", using default");
            languageCode = DEFAULT_LANGUAGE;
        }
        
        // Save preference
        preferences.edit().putString(KEY_SELECTED_LANGUAGE, languageCode).apply();
        
        // Apply language
        applyLanguage(languageCode);
        
        Log.d(TAG, "Language set to: " + languageCode);
    }
    
    /**
     * Apply language to context
     */
    public void applyLanguage(String languageCode) {
        try {
            Locale locale = new Locale(languageCode);
            Locale.setDefault(locale);
            
            Resources resources = context.getResources();
            Configuration configuration = resources.getConfiguration();
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                configuration.setLocale(locale);
            } else {
                configuration.locale = locale;
            }
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                context.createConfigurationContext(configuration);
            } else {
                resources.updateConfiguration(configuration, resources.getDisplayMetrics());
            }
            
            Log.d(TAG, "Language applied: " + languageCode);
            
        } catch (Exception e) {
            Log.e(TAG, "Error applying language: " + languageCode, e);
        }
    }
    
    /**
     * Initialize language on app start
     */
    public void initializeLanguage() {
        String savedLanguage = getCurrentLanguage();
        applyLanguage(savedLanguage);
        Log.d(TAG, "Language initialized: " + savedLanguage);
    }
    
    /**
     * Check if language is supported
     */
    public static boolean isLanguageSupported(String languageCode) {
        for (Language language : SUPPORTED_LANGUAGES) {
            if (language.code.equals(languageCode)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Get language by code
     */
    public static Language getLanguageByCode(String code) {
        for (Language language : SUPPORTED_LANGUAGES) {
            if (language.code.equals(code)) {
                return language;
            }
        }
        return null;
    }
    
    /**
     * Get system language if supported, otherwise return default
     */
    public static String getSystemLanguageOrDefault() {
        String systemLanguage = Locale.getDefault().getLanguage();
        return isLanguageSupported(systemLanguage) ? systemLanguage : DEFAULT_LANGUAGE;
    }
    
    /**
     * Create context with specific language
     */
    public static Context createLanguageContext(Context context, String languageCode) {
        try {
            Locale locale = new Locale(languageCode);
            Configuration configuration = new Configuration(context.getResources().getConfiguration());
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                configuration.setLocale(locale);
                return context.createConfigurationContext(configuration);
            } else {
                configuration.locale = locale;
                context.getResources().updateConfiguration(configuration, context.getResources().getDisplayMetrics());
                return context;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error creating language context", e);
            return context;
        }
    }
    
    /**
     * Get localized string for specific language
     */
    public String getLocalizedString(int stringResId, String languageCode) {
        try {
            Context languageContext = createLanguageContext(context, languageCode);
            return languageContext.getString(stringResId);
        } catch (Exception e) {
            Log.e(TAG, "Error getting localized string", e);
            return context.getString(stringResId);
        }
    }
    
    /**
     * Check if current language is RTL (Right-to-Left)
     */
    public boolean isRTL() {
        String currentLanguage = getCurrentLanguage();
        return "ar".equals(currentLanguage); // Only Arabic is RTL in our supported languages
    }
    
    /**
     * Get language display name in current locale
     */
    public String getLanguageDisplayName(String languageCode) {
        Language language = getLanguageByCode(languageCode);
        return language != null ? language.nativeName : languageCode;
    }

    /**
     * Get speech recognition locale for current language
     */
    public java.util.Locale getSpeechRecognitionLocale() {
        String currentLang = getCurrentLanguage();
        switch (currentLang) {
            case LANG_MALAYALAM:
                return new java.util.Locale("ml", "IN");
            case LANG_HINDI:
                return new java.util.Locale("hi", "IN");
            case LANG_ARABIC:
                return new java.util.Locale("ar", "SA");
            case LANG_ENGLISH:
            default:
                return java.util.Locale.ENGLISH;
        }
    }

    /**
     * Get TTS locale for current language
     */
    public java.util.Locale getTTSLocale() {
        return getSpeechRecognitionLocale(); // Same as speech recognition
    }

    /**
     * Get language instruction for AI to respond in current language
     */
    public String getLanguageInstruction() {
        String currentLang = getCurrentLanguage();
        switch (currentLang) {
            case LANG_MALAYALAM:
                return "ദയവായി മലയാളത്തിൽ മറുപടി നൽകുക. എന്നാൽ റോബോട്ട് കമാൻഡുകൾ ഇംഗ്ലീഷിൽ തന്നെ അയയ്ക്കുക.";
            case LANG_HINDI:
                return "कृपया हिंदी में उत्तर दें। लेकिन रोबोट कमांड अंग्रेजी में ही भेजें।";
            case LANG_ARABIC:
                return "يرجى الرد باللغة العربية. لكن أرسل أوامر الروبوت باللغة الإنجليزية.";
            default:
                return "Please respond in English.";
        }
    }

    /**
     * Get system prompt for AI in current language
     */
    public String getSystemPrompt() {
        String currentLang = getCurrentLanguage();
        switch (currentLang) {
            case LANG_MALAYALAM:
                return "നിങ്ങൾ ഒരു സഹായകരമായ STEM റോബോട്ട് അസിസ്റ്റന്റാണ്. വിദ്യാഭ്യാസപരമായ റോബോട്ടിക്സിൽ വിദ്യാർത്ഥികളെ സഹായിക്കുക.";
            case LANG_HINDI:
                return "आप एक सहायक STEM रोबोट असिस्टेंट हैं। शैक्षिक रोबोटिक्स में छात्रों की सहायता करें।";
            case LANG_ARABIC:
                return "أنت مساعد روبوت STEM مفيد. ساعد الطلاب في الروبوتات التعليمية.";
            default:
                return "You are a helpful STEM robot assistant. Help students with educational robotics.";
        }
    }

    /**
     * Translate robot command to English for ESP32 execution
     */
    public String translateCommandToEnglish(String input) {
        String currentLang = getCurrentLanguage();
        if (LANG_ENGLISH.equals(currentLang)) {
            return input; // Already English
        }

        String lowerInput = input.toLowerCase();

        // Malayalam command translations
        if (LANG_MALAYALAM.equals(currentLang)) {
            if (lowerInput.contains("മുന്നോട്ട്") || lowerInput.contains("മുന്നിലേക്ക്")) return "move forward";
            if (lowerInput.contains("പിന്നോട്ട്") || lowerInput.contains("പിന്നിലേക്ക്")) return "move backward";
            if (lowerInput.contains("ഇടത്തോട്ട്") || lowerInput.contains("ഇടതുവശത്തേക്ക്")) return "turn left";
            if (lowerInput.contains("വലത്തോട്ട്") || lowerInput.contains("വലതുവശത്തേക്ക്")) return "turn right";
            if (lowerInput.contains("നിർത്തുക") || lowerInput.contains("സ്റ്റോപ്പ്")) return "stop";
            if (lowerInput.contains("കൈ വീശുക") || lowerInput.contains("ഹലോ പറയുക")) return "wave";
            if (lowerInput.contains("ചൂണ്ടുക") || lowerInput.contains("കാണിക്കുക")) return "point";
            if (lowerInput.contains("വിശ്രമിക്കുക") || lowerInput.contains("റെസ്റ്റ്")) return "rest";
        }

        // Hindi command translations
        if (LANG_HINDI.equals(currentLang)) {
            if (lowerInput.contains("आगे") || lowerInput.contains("आगे बढ़ो")) return "move forward";
            if (lowerInput.contains("पीछे") || lowerInput.contains("पीछे जाओ")) return "move backward";
            if (lowerInput.contains("बाएं") || lowerInput.contains("बाईं ओर")) return "turn left";
            if (lowerInput.contains("दाएं") || lowerInput.contains("दाईं ओर")) return "turn right";
            if (lowerInput.contains("रुको") || lowerInput.contains("स्टॉप")) return "stop";
            if (lowerInput.contains("हाथ हिलाओ") || lowerInput.contains("नमस्ते")) return "wave";
            if (lowerInput.contains("इशारा") || lowerInput.contains("दिखाओ")) return "point";
            if (lowerInput.contains("आराम") || lowerInput.contains("रेस्ट")) return "rest";
        }

        // Arabic command translations
        if (LANG_ARABIC.equals(currentLang)) {
            if (lowerInput.contains("أمام") || lowerInput.contains("للأمام")) return "move forward";
            if (lowerInput.contains("خلف") || lowerInput.contains("للخلف")) return "move backward";
            if (lowerInput.contains("يسار") || lowerInput.contains("يسارا")) return "turn left";
            if (lowerInput.contains("يمين") || lowerInput.contains("يمينا")) return "turn right";
            if (lowerInput.contains("توقف") || lowerInput.contains("قف")) return "stop";
            if (lowerInput.contains("لوح") || lowerInput.contains("مرحبا")) return "wave";
            if (lowerInput.contains("أشر") || lowerInput.contains("اشر")) return "point";
            if (lowerInput.contains("استرح") || lowerInput.contains("راحة")) return "rest";
        }

        return input; // Return original if no translation found
    }
}
