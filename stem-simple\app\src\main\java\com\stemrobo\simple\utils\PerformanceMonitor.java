package com.stemrobo.simple.utils;

import android.app.ActivityManager;
import android.content.Context;
import android.os.Debug;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Performance Monitor for STEM Simple Robot
 * Monitors memory usage, performance metrics, and system health
 */
public class PerformanceMonitor {
    private static final String TAG = "PerformanceMonitor";
    private static final long MONITORING_INTERVAL = 30000; // 30 seconds
    private static final long MEMORY_WARNING_THRESHOLD = 50 * 1024 * 1024; // 50MB
    
    private final Context context;
    private final ActivityManager activityManager;
    private final Handler monitoringHandler;
    private final AtomicLong lastGCTime;
    private boolean isMonitoring = false;
    
    // Performance metrics
    private long maxMemoryUsed = 0;
    private long totalGCCount = 0;
    private long averageResponseTime = 0;
    
    public interface PerformanceCallback {
        void onMemoryWarning(long currentMemory, long maxMemory);
        void onPerformanceUpdate(PerformanceMetrics metrics);
    }
    
    public static class PerformanceMetrics {
        public final long memoryUsed;
        public final long maxMemory;
        public final long freeMemory;
        public final double memoryUsagePercent;
        public final long gcCount;
        public final long responseTime;
        
        public PerformanceMetrics(long memoryUsed, long maxMemory, long freeMemory, 
                                long gcCount, long responseTime) {
            this.memoryUsed = memoryUsed;
            this.maxMemory = maxMemory;
            this.freeMemory = freeMemory;
            this.memoryUsagePercent = (double) memoryUsed / maxMemory * 100;
            this.gcCount = gcCount;
            this.responseTime = responseTime;
        }
        
        @Override
        public String toString() {
            return String.format("Memory: %.1f%% (%d/%d MB), GC: %d, Response: %dms",
                memoryUsagePercent, memoryUsed / (1024*1024), maxMemory / (1024*1024), 
                gcCount, responseTime);
        }
    }
    
    private PerformanceCallback performanceCallback;
    
    public PerformanceMonitor(Context context) {
        this.context = context.getApplicationContext();
        this.activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        this.monitoringHandler = new Handler(Looper.getMainLooper());
        this.lastGCTime = new AtomicLong(System.currentTimeMillis());
    }
    
    public void setPerformanceCallback(PerformanceCallback callback) {
        this.performanceCallback = callback;
    }
    
    /**
     * Start performance monitoring
     */
    public void startMonitoring() {
        if (isMonitoring) {
            Log.w(TAG, "Performance monitoring already started");
            return;
        }
        
        isMonitoring = true;
        scheduleNextMonitoring();
        Log.d(TAG, "Performance monitoring started");
    }
    
    /**
     * Stop performance monitoring
     */
    public void stopMonitoring() {
        isMonitoring = false;
        monitoringHandler.removeCallbacksAndMessages(null);
        Log.d(TAG, "Performance monitoring stopped");
    }
    
    private void scheduleNextMonitoring() {
        if (!isMonitoring) return;
        
        monitoringHandler.postDelayed(() -> {
            try {
                performMonitoringCheck();
                scheduleNextMonitoring();
            } catch (Exception e) {
                Log.e(TAG, "Error during monitoring check", e);
                scheduleNextMonitoring(); // Continue monitoring despite errors
            }
        }, MONITORING_INTERVAL);
    }
    
    private void performMonitoringCheck() {
        PerformanceMetrics metrics = getCurrentMetrics();
        
        // Update max memory used
        if (metrics.memoryUsed > maxMemoryUsed) {
            maxMemoryUsed = metrics.memoryUsed;
        }
        
        // Check for memory warnings
        if (metrics.memoryUsed > MEMORY_WARNING_THRESHOLD) {
            Log.w(TAG, "Memory usage high: " + metrics.memoryUsed / (1024*1024) + " MB");
            if (performanceCallback != null) {
                performanceCallback.onMemoryWarning(metrics.memoryUsed, metrics.maxMemory);
            }
        }
        
        // Log performance metrics
        Log.d(TAG, "Performance: " + metrics.toString());
        
        // Notify callback
        if (performanceCallback != null) {
            performanceCallback.onPerformanceUpdate(metrics);
        }
    }
    
    /**
     * Get current performance metrics
     */
    public PerformanceMetrics getCurrentMetrics() {
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        
        // Get GC count (approximation)
        long currentGCCount = Debug.getGlobalGcInvocationCount();
        
        return new PerformanceMetrics(usedMemory, maxMemory, freeMemory, currentGCCount, averageResponseTime);
    }
    
    /**
     * Force garbage collection if memory is low
     */
    public void forceGCIfNeeded() {
        PerformanceMetrics metrics = getCurrentMetrics();
        
        if (metrics.memoryUsagePercent > 80) {
            Log.d(TAG, "Memory usage high (" + String.format("%.1f", metrics.memoryUsagePercent) + 
                  "%), forcing garbage collection");
            System.gc();
            lastGCTime.set(System.currentTimeMillis());
            totalGCCount++;
        }
    }
    
    /**
     * Measure response time for operations
     */
    public long measureResponseTime(Runnable operation) {
        long startTime = System.currentTimeMillis();
        try {
            operation.run();
        } catch (Exception e) {
            Log.e(TAG, "Error during operation measurement", e);
        }
        long endTime = System.currentTimeMillis();
        long responseTime = endTime - startTime;
        
        // Update average response time
        averageResponseTime = (averageResponseTime + responseTime) / 2;
        
        return responseTime;
    }
    
    /**
     * Get memory usage summary
     */
    public String getMemorySummary() {
        PerformanceMetrics metrics = getCurrentMetrics();
        return String.format("Memory: %.1f%% used (%d MB / %d MB)",
            metrics.memoryUsagePercent,
            metrics.memoryUsed / (1024*1024),
            metrics.maxMemory / (1024*1024));
    }
    
    /**
     * Check if system is under memory pressure
     */
    public boolean isMemoryPressure() {
        PerformanceMetrics metrics = getCurrentMetrics();
        return metrics.memoryUsagePercent > 75;
    }
    
    /**
     * Get system memory info
     */
    public ActivityManager.MemoryInfo getSystemMemoryInfo() {
        ActivityManager.MemoryInfo memoryInfo = new ActivityManager.MemoryInfo();
        if (activityManager != null) {
            activityManager.getMemoryInfo(memoryInfo);
        }
        return memoryInfo;
    }
    
    /**
     * Log detailed performance report
     */
    public void logPerformanceReport() {
        PerformanceMetrics metrics = getCurrentMetrics();
        ActivityManager.MemoryInfo systemMemory = getSystemMemoryInfo();
        
        Log.i(TAG, "=== Performance Report ===");
        Log.i(TAG, "App Memory: " + metrics.toString());
        Log.i(TAG, "System Memory Available: " + systemMemory.availMem / (1024*1024) + " MB");
        Log.i(TAG, "System Memory Total: " + systemMemory.totalMem / (1024*1024) + " MB");
        Log.i(TAG, "System Low Memory: " + systemMemory.lowMemory);
        Log.i(TAG, "Max Memory Used: " + maxMemoryUsed / (1024*1024) + " MB");
        Log.i(TAG, "Total GC Count: " + totalGCCount);
        Log.i(TAG, "Average Response Time: " + averageResponseTime + " ms");
        Log.i(TAG, "========================");
    }
    
    /**
     * Cleanup resources
     */
    public void cleanup() {
        stopMonitoring();
        Log.d(TAG, "Performance monitor cleaned up");
    }
}
