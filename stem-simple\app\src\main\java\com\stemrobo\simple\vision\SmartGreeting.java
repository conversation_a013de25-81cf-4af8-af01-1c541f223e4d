package com.stemrobo.simple.vision;

import android.content.Context;
import android.content.SharedPreferences;
import android.speech.tts.TextToSpeech;
import android.util.Log;
import com.google.mlkit.vision.face.Face;
import com.stemrobo.simple.robot.SimpleRobotController;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Smart Greeting System for STEM Simple Robot
 * Combines face detection with ultrasonic distance measurement
 * to provide intelligent greeting behavior
 */
public class SmartGreeting implements TextToSpeech.OnInitListener {
    private static final String TAG = "SmartGreeting";
    private static final String PREFS_NAME = "smart_greeting_settings";
    
    // Settings keys
    private static final String KEY_ENABLED = "greeting_enabled";
    private static final String KEY_DISTANCE_THRESHOLD = "distance_threshold";
    private static final String KEY_COOLDOWN_PERIOD = "cooldown_period";
    
    // Default settings
    private static final boolean DEFAULT_ENABLED = true;
    private static final float DEFAULT_DISTANCE_THRESHOLD = 30.0f; // 30cm
    private static final long DEFAULT_COOLDOWN_PERIOD = 10000; // 10 seconds
    
    private final Context context;
    private final SimpleRobotController robotController;
    private final SharedPreferences preferences;
    private final ExecutorService executorService;
    
    // TTS for greeting speech
    private TextToSpeech textToSpeech;
    private boolean ttsReady = false;
    
    // Greeting state
    private boolean greetingEnabled;
    private float distanceThreshold;
    private long cooldownPeriod;
    private long lastGreetingTime = 0;
    private boolean faceDetected = false;
    private long faceDetectionStartTime = 0;
    private static long FACE_DETECTION_DURATION = 2000; // 2 seconds (configurable)

    // Face tracking variables for new face detection
    private int lastFaceCount = 0;
    private boolean greetingInProgress = false;
    private float currentDistance = 999.0f; // Current distance from ultrasonic sensor
    private long lastDistanceUpdate = 0;
    private boolean newFaceDetected = false;

    // Distance-based reset logic
    private boolean personOutOfRange = true; // Start assuming no one is there
    private long outOfRangeStartTime = 0;
    private long OUT_OF_RANGE_RESET_TIME = 10000; // 10 seconds (configurable)
    private boolean canGreetAgain = true; // Can greet when person returns

    // Callbacks
    private GreetingCallback greetingCallback;
    
    public interface GreetingCallback {
        void onGreetingStarted();
        void onGreetingCompleted();
        void onGreetingError(String error);
    }
    
    public SmartGreeting(Context context, SimpleRobotController robotController) {
        this.context = context;
        this.robotController = robotController;
        this.preferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        this.executorService = Executors.newSingleThreadExecutor();
        
        loadSettings();
        initializeTTS();
        
        Log.d(TAG, "Smart greeting initialized - Enabled: " + greetingEnabled + 
                   ", Distance: " + distanceThreshold + "cm, Cooldown: " + cooldownPeriod + "ms");
    }
    
    private void loadSettings() {
        greetingEnabled = preferences.getBoolean(KEY_ENABLED, DEFAULT_ENABLED);
        distanceThreshold = preferences.getFloat(KEY_DISTANCE_THRESHOLD, DEFAULT_DISTANCE_THRESHOLD);
        cooldownPeriod = preferences.getLong(KEY_COOLDOWN_PERIOD, DEFAULT_COOLDOWN_PERIOD);
    }
    
    private void initializeTTS() {
        textToSpeech = new TextToSpeech(context, this);
    }
    
    @Override
    public void onInit(int status) {
        if (status == TextToSpeech.SUCCESS) {
            int result = textToSpeech.setLanguage(Locale.US);
            if (result == TextToSpeech.LANG_MISSING_DATA || result == TextToSpeech.LANG_NOT_SUPPORTED) {
                Log.e(TAG, "TTS language not supported");
                ttsReady = false;
            } else {
                ttsReady = true;
                Log.d(TAG, "TTS initialized successfully");
            }
        } else {
            Log.e(TAG, "TTS initialization failed");
            ttsReady = false;
        }
    }

    /**
     * Update distance from continuous monitoring (every 0.1 seconds)
     */
    public void updateDistance(float distance) {
        currentDistance = distance;
        lastDistanceUpdate = System.currentTimeMillis();
        long currentTime = System.currentTimeMillis();

        // Check if person is within detection range
        boolean withinRange = distance <= distanceThreshold;

        // Handle distance-based state changes
        if (withinRange) {
            // Person is within range
            if (personOutOfRange) {
                // Person just entered range
                personOutOfRange = false;
                Log.d(TAG, "Person entered detection range: " + distance + "cm");
            }

            // If we have a face detected and can greet, check for greeting
            if (faceDetected && !greetingInProgress && canGreetAgain) {
                if (currentTime - faceDetectionStartTime >= FACE_DETECTION_DURATION) {
                    if (currentTime - lastGreetingTime >= cooldownPeriod) {
                        Log.d(TAG, "All conditions met - executing greeting");
                        executeGreeting();
                        canGreetAgain = false; // Prevent immediate re-greeting
                    }
                }
            }
        } else {
            // Person is out of range
            if (!personOutOfRange) {
                // Person just left range
                personOutOfRange = true;
                outOfRangeStartTime = currentTime;
                Log.d(TAG, "Person left detection range: " + distance + "cm");
            } else {
                // Person has been out of range - check if enough time has passed
                if (currentTime - outOfRangeStartTime >= OUT_OF_RANGE_RESET_TIME) {
                    if (!canGreetAgain) {
                        canGreetAgain = true;
                        Log.d(TAG, "Person out of range for " + (OUT_OF_RANGE_RESET_TIME/1000) + "s - can greet again");
                    }
                }
            }
        }
    }

    /**
     * Process detected faces for smart greeting
     */
    public void processFaces(List<Face> faces) {
        if (!greetingEnabled) {
            return;
        }

        long currentTime = System.currentTimeMillis();
        int currentFaceCount = faces.size();

        // Detect new faces (face count increased from 0)
        if (currentFaceCount > 0 && lastFaceCount == 0) {
            newFaceDetected = true;
            Log.d(TAG, "New face detected! Face count: " + currentFaceCount);
        }

        // Check if faces are detected
        if (currentFaceCount > 0) {
            if (!faceDetected || newFaceDetected) {
                // Start face detection timer for new face
                faceDetected = true;
                faceDetectionStartTime = currentTime;
                newFaceDetected = false;
                Log.d(TAG, "Face detection started for new face");
            }
            // Note: Greeting logic is now handled in updateDistance() method
            // This ensures real-time distance checking every 0.1 seconds
        } else {
            // No faces detected, reset detection state
            if (faceDetected) {
                faceDetected = false;
                newFaceDetected = false;
                Log.d(TAG, "Face detection ended - no faces visible");
            }
        }

        lastFaceCount = currentFaceCount;
    }
    

    
    private void executeGreeting() {
        if (greetingInProgress) {
            Log.d(TAG, "Greeting already in progress, skipping");
            return;
        }

        greetingInProgress = true;
        executorService.execute(() -> {
            try {
                Log.d(TAG, "Executing smart greeting");
                lastGreetingTime = System.currentTimeMillis();

                if (greetingCallback != null) {
                    greetingCallback.onGreetingStarted();
                }

                // Perform handshake gesture
                if (robotController != null) {
                    robotController.performHandshake();
                }

                // Speak greeting
                speakGreeting("Hi there! Nice to meet you!");
                
                // Reset face detection state
                faceDetected = false;
                greetingInProgress = false;

                if (greetingCallback != null) {
                    greetingCallback.onGreetingCompleted();
                }

                Log.d(TAG, "Smart greeting completed");

            } catch (Exception e) {
                Log.e(TAG, "Error executing greeting", e);
                greetingInProgress = false; // Reset on error too
                if (greetingCallback != null) {
                    greetingCallback.onGreetingError("Greeting execution failed: " + e.getMessage());
                }
            }
        });
    }
    
    private void speakGreeting(String message) {
        if (ttsReady && textToSpeech != null) {
            textToSpeech.speak(message, TextToSpeech.QUEUE_FLUSH, null, "greeting");
            Log.d(TAG, "Speaking greeting: " + message);
        } else {
            Log.w(TAG, "TTS not ready, cannot speak greeting");
        }
    }
    
    // ===== SETTINGS METHODS =====
    
    public void setGreetingEnabled(boolean enabled) {
        this.greetingEnabled = enabled;
        preferences.edit().putBoolean(KEY_ENABLED, enabled).apply();
        Log.d(TAG, "Greeting enabled: " + enabled);
    }
    
    public void setDistanceThreshold(float threshold) {
        this.distanceThreshold = threshold;
        preferences.edit().putFloat(KEY_DISTANCE_THRESHOLD, threshold).apply();
        Log.d(TAG, "Distance threshold set to: " + threshold + "cm");
    }
    
    public void setCooldownPeriod(long cooldown) {
        this.cooldownPeriod = cooldown;
        preferences.edit().putLong(KEY_COOLDOWN_PERIOD, cooldown).apply();
        Log.d(TAG, "Cooldown period set to: " + cooldown + "ms");
    }
    
    // ===== GETTERS =====
    
    public boolean isGreetingEnabled() {
        return greetingEnabled;
    }
    
    public float getDistanceThreshold() {
        return distanceThreshold;
    }
    
    public long getCooldownPeriod() {
        return cooldownPeriod;
    }
    
    public boolean isFaceDetected() {
        return faceDetected;
    }
    
    public long getTimeSinceLastGreeting() {
        return System.currentTimeMillis() - lastGreetingTime;
    }
    
    // ===== CALLBACK SETTER =====
    
    public void setGreetingCallback(GreetingCallback callback) {
        this.greetingCallback = callback;
    }
    
    // ===== MANUAL GREETING =====
    
    public void triggerManualGreeting() {
        if (!greetingEnabled) {
            Log.w(TAG, "Manual greeting requested but greeting is disabled");
            return;
        }
        
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastGreetingTime < cooldownPeriod) {
            Log.w(TAG, "Manual greeting blocked by cooldown period");
            if (greetingCallback != null) {
                greetingCallback.onGreetingError("Greeting in cooldown period");
            }
            return;
        }
        
        executeGreeting();
    }
    
    // ===== ADVANCED SETTINGS =====

    public void setFaceDetectionDuration(long duration) {
        if (duration >= 500 && duration <= 10000) {
            FACE_DETECTION_DURATION = duration;
            Log.d(TAG, "Face detection duration set to " + duration + "ms");
        }
    }

    public void setOutOfRangeResetTime(long time) {
        if (time >= 1000 && time <= 60000) { // 1-60 seconds
            OUT_OF_RANGE_RESET_TIME = time;
            Log.d(TAG, "Out of range reset time set to: " + time + "ms");
        }
    }

    public void setHandshakeDuration(long duration) {
        if (duration >= 1000 && duration <= 30000) {
            // This would be used to configure the handshake gesture duration
            Log.d(TAG, "Handshake duration set to " + duration + "ms");
            // TODO: Send duration to ESP32 if needed
        }
    }

    public long getFaceDetectionDuration() {
        return FACE_DETECTION_DURATION;
    }

    // ===== CLEANUP =====

    public void cleanup() {
        if (textToSpeech != null) {
            textToSpeech.stop();
            textToSpeech.shutdown();
        }
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
        Log.d(TAG, "Smart greeting cleaned up");
    }
}
