package com.stemrobo.simple.ai;

import android.util.Log;
import com.stemrobo.simple.robot.SimpleRobotController;
import com.stemrobo.simple.vision.SmartGreeting;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Command Parser for STEM Simple Robot
 * Executes AI commands on the robot hardware
 */
public class CommandParser {
    private static final String TAG = "CommandParser";
    
    private final SimpleRobotController robotController;
    private final SmartGreeting smartGreeting;
    private final ExecutorService executorService;
    private CommandExecutionCallback executionCallback;
    
    public interface CommandExecutionCallback {
        void onCommandStarted(AICommand command);
        void onCommandCompleted(AICommand command);
        void onCommandError(AICommand command, String error);
        void onAllCommandsCompleted();
    }
    
    public CommandParser(SimpleRobotController robotController, SmartGreeting smartGreeting) {
        this.robotController = robotController;
        this.smartGreeting = smartGreeting;
        this.executorService = Executors.newSingleThreadExecutor();
    }
    
    public void setExecutionCallback(CommandExecutionCallback callback) {
        this.executionCallback = callback;
    }
    
    /**
     * Execute array of AI commands sequentially
     */
    public void executeCommands(AICommand[] commands) {
        if (commands == null || commands.length == 0) {
            Log.d(TAG, "No commands to execute");
            if (executionCallback != null) {
                executionCallback.onAllCommandsCompleted();
            }
            return;
        }
        
        Log.d(TAG, "Executing " + commands.length + " commands");
        
        executorService.execute(() -> {
            for (AICommand command : commands) {
                try {
                    executeCommand(command);
                    
                    // Add delay between commands if needed
                    if (command.hasDuration()) {
                        Thread.sleep(command.getDuration());
                    } else {
                        Thread.sleep(500); // Default 500ms delay
                    }
                    
                } catch (InterruptedException e) {
                    Log.w(TAG, "Command execution interrupted");
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    Log.e(TAG, "Error executing command: " + command, e);
                    if (executionCallback != null) {
                        executionCallback.onCommandError(command, e.getMessage());
                    }
                }
            }
            
            if (executionCallback != null) {
                executionCallback.onAllCommandsCompleted();
            }
        });
    }
    
    /**
     * Execute a single AI command
     */
    public void executeCommand(AICommand command) {
        if (command == null) {
            Log.w(TAG, "Null command received");
            return;
        }
        
        Log.d(TAG, "Executing command: " + command);
        
        if (executionCallback != null) {
            executionCallback.onCommandStarted(command);
        }
        
        try {
            switch (command.getType().toLowerCase()) {
                case "movement":
                    executeMovementCommand(command);
                    break;
                case "servo":
                    executeServoCommand(command);
                    break;
                case "sensor":
                    executeSensorCommand(command);
                    break;
                case "greeting":
                    executeGreetingCommand(command);
                    break;
                default:
                    Log.w(TAG, "Unknown command type: " + command.getType());
                    if (executionCallback != null) {
                        executionCallback.onCommandError(command, "Unknown command type");
                    }
                    return;
            }
            
            if (executionCallback != null) {
                executionCallback.onCommandCompleted(command);
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error executing command: " + command, e);
            if (executionCallback != null) {
                executionCallback.onCommandError(command, e.getMessage());
            }
        }
    }
    
    private void executeMovementCommand(AICommand command) {
        if (robotController == null) {
            throw new RuntimeException("Robot controller not available");
        }
        
        String action = command.getAction().toLowerCase();
        
        switch (action) {
            case "forward":
                robotController.moveForward();
                break;
            case "backward":
                robotController.moveBackward();
                break;
            case "left":
                robotController.turnLeft();
                break;
            case "right":
                robotController.turnRight();
                break;
            case "stop":
                robotController.stopMovement();
                break;
            default:
                throw new RuntimeException("Unknown movement action: " + action);
        }
        
        Log.d(TAG, "Movement command executed: " + action);
    }
    
    private void executeServoCommand(AICommand command) {
        if (robotController == null) {
            throw new RuntimeException("Robot controller not available");
        }
        
        String action = command.getAction().toLowerCase();
        
        switch (action) {
            case "wave":
                robotController.performWave();
                break;
            case "point":
                robotController.performPoint();
                break;
            case "rest":
                robotController.moveToRestPosition();
                break;
            case "handshake":
                robotController.performHandshake();
                break;
            default:
                throw new RuntimeException("Unknown servo action: " + action);
        }
        
        Log.d(TAG, "Servo command executed: " + action);
    }
    
    private void executeSensorCommand(AICommand command) {
        if (robotController == null) {
            throw new RuntimeException("Robot controller not available");
        }
        
        String action = command.getAction().toLowerCase();
        
        switch (action) {
            case "get_distance":
                robotController.getDistance(new SimpleRobotController.DistanceCallback() {
                    @Override
                    public void onDistanceReceived(float distance) {
                        Log.d(TAG, "Distance sensor reading: " + distance + " cm");
                    }
                    
                    @Override
                    public void onDistanceError(String error) {
                        Log.e(TAG, "Distance sensor error: " + error);
                    }
                });
                break;
            case "start_distance_stream":
                robotController.startDistanceStreaming();
                break;
            case "stop_distance_stream":
                robotController.stopDistanceStreaming();
                break;
            default:
                throw new RuntimeException("Unknown sensor action: " + action);
        }
        
        Log.d(TAG, "Sensor command executed: " + action);
    }
    
    private void executeGreetingCommand(AICommand command) {
        if (smartGreeting == null) {
            throw new RuntimeException("Smart greeting not available");
        }
        
        String action = command.getAction().toLowerCase();
        
        switch (action) {
            case "trigger_greeting":
                smartGreeting.triggerManualGreeting();
                break;
            default:
                throw new RuntimeException("Unknown greeting action: " + action);
        }
        
        Log.d(TAG, "Greeting command executed: " + action);
    }
    
    /**
     * Stop all ongoing command execution
     */
    public void stopExecution() {
        Log.d(TAG, "Stopping command execution");
        
        // Stop robot movement
        if (robotController != null) {
            robotController.stopMovement();
        }
        
        // Interrupt executor
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdownNow();
        }
    }
    
    /**
     * Check if commands are currently being executed
     */
    public boolean isExecuting() {
        return executorService != null && !executorService.isShutdown();
    }
    
    public void cleanup() {
        stopExecution();
        Log.d(TAG, "Command parser cleaned up");
    }
}
