package com.stemrobo.simple.ai;

/**
 * AI Command structure for STEM Simple Robot
 * Represents executable commands parsed from AI responses
 */
public class AICommand {
    private String type;
    private String action;
    private int duration; // Duration in milliseconds (for movement commands)
    private String parameter; // Additional parameter if needed
    
    public AICommand() {
        // Default constructor for JSON parsing
    }
    
    public AICommand(String type, String action, int duration) {
        this.type = type;
        this.action = action;
        this.duration = duration;
    }
    
    public AICommand(String type, String action, int duration, String parameter) {
        this.type = type;
        this.action = action;
        this.duration = duration;
        this.parameter = parameter;
    }
    
    // Getters
    public String getType() {
        return type;
    }
    
    public String getAction() {
        return action;
    }
    
    public int getDuration() {
        return duration;
    }
    
    public String getParameter() {
        return parameter;
    }
    
    // Setters
    public void setType(String type) {
        this.type = type;
    }
    
    public void setAction(String action) {
        this.action = action;
    }
    
    public void setDuration(int duration) {
        this.duration = duration;
    }
    
    public void setParameter(String parameter) {
        this.parameter = parameter;
    }
    
    // Utility methods
    public boolean isMovementCommand() {
        return "movement".equalsIgnoreCase(type);
    }
    
    public boolean isServoCommand() {
        return "servo".equalsIgnoreCase(type);
    }
    
    public boolean isSensorCommand() {
        return "sensor".equalsIgnoreCase(type);
    }
    
    public boolean isGreetingCommand() {
        return "greeting".equalsIgnoreCase(type);
    }
    
    public boolean hasParameter() {
        return parameter != null && !parameter.trim().isEmpty();
    }
    
    public boolean hasDuration() {
        return duration > 0;
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("AICommand{");
        sb.append("type='").append(type).append('\'');
        sb.append(", action='").append(action).append('\'');
        if (duration > 0) {
            sb.append(", duration=").append(duration);
        }
        if (parameter != null) {
            sb.append(", parameter='").append(parameter).append('\'');
        }
        sb.append('}');
        return sb.toString();
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        AICommand aiCommand = (AICommand) obj;
        
        if (duration != aiCommand.duration) return false;
        if (type != null ? !type.equals(aiCommand.type) : aiCommand.type != null) return false;
        if (action != null ? !action.equals(aiCommand.action) : aiCommand.action != null) return false;
        return parameter != null ? parameter.equals(aiCommand.parameter) : aiCommand.parameter == null;
    }
    
    @Override
    public int hashCode() {
        int result = type != null ? type.hashCode() : 0;
        result = 31 * result + (action != null ? action.hashCode() : 0);
        result = 31 * result + duration;
        result = 31 * result + (parameter != null ? parameter.hashCode() : 0);
        return result;
    }
}
