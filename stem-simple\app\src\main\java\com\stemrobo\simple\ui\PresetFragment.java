package com.stemrobo.simple.ui;

import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.Toast;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.stemrobo.simple.MainActivity;
import com.stemrobo.simple.R;
import com.stemrobo.simple.ai.*;
import com.stemrobo.simple.robot.SimpleRobotController;
import com.stemrobo.simple.activities.PresetEditorActivity;
import com.stemrobo.simple.ui.PresetAdapter;

import java.util.ArrayList;
import java.util.List;

/**
 * Fragment for managing robot presets
 */
public class PresetFragment extends Fragment {
    private static final String TAG = "PresetFragment";
    private static final int REQUEST_CREATE_PRESET = 1001;
    private static final int REQUEST_EDIT_PRESET = 1002;
    
    // UI Components
    private RecyclerView presetsRecyclerView;
    private FloatingActionButton fabCreatePreset;
    private Button btnRefreshPresets;
    private View emptyState;
    
    // Data and Adapters
    private PresetAdapter presetAdapter;
    private List<RobotPreset> presets;
    
    // System Components
    private PresetManager presetManager;
    private CommandParser commandParser;
    private SimpleRobotController robotController;
    
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_preset, container, false);
    }
    
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        initializeViews(view);
        initializeSystemComponents();
        setupRecyclerView();
        setupButtonListeners();
        loadPresets();
    }
    
    private void initializeViews(View view) {
        presetsRecyclerView = view.findViewById(R.id.presets_recycler_view);
        fabCreatePreset = view.findViewById(R.id.fab_create_preset);
        btnRefreshPresets = view.findViewById(R.id.btn_refresh_presets);
        emptyState = view.findViewById(R.id.empty_state);
    }
    
    private void initializeSystemComponents() {
        // Get system components from MainActivity
        if (getActivity() instanceof MainActivity) {
            MainActivity mainActivity = (MainActivity) getActivity();
            robotController = mainActivity.getRobotController();
        }
        
        // Initialize command parser and preset manager
        commandParser = new CommandParser(robotController, null);
        presetManager = new PresetManager(getContext(), commandParser);
        
        // Set preset callback
        presetManager.setPresetCallback(new PresetManager.PresetCallback() {
            @Override
            public void onPresetCreated(RobotPreset preset) {
                loadPresets();
                showMessage("Preset created: " + preset.getName());
            }
            
            @Override
            public void onPresetExecuted(RobotPreset preset) {
                showMessage("Executing preset: " + preset.getName());
            }
            
            @Override
            public void onPresetError(String error) {
                showError("Preset error: " + error);
            }
            
            @Override
            public void onPresetsLoaded(List<RobotPreset> loadedPresets) {
                updatePresetList(loadedPresets);
            }
        });
    }
    
    private void setupRecyclerView() {
        presets = new ArrayList<>();
        presetAdapter = new PresetAdapter(presets, new PresetAdapter.OnPresetClickListener() {
            @Override
            public void onPresetClick(RobotPreset preset) {
                showPresetOptions(preset);
            }
        });
        
        presetsRecyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        presetsRecyclerView.setAdapter(presetAdapter);
    }
    
    private void setupButtonListeners() {
        fabCreatePreset.setOnClickListener(v -> createNewPreset());
        btnRefreshPresets.setOnClickListener(v -> loadPresets());
    }
    
    private void loadPresets() {
        List<RobotPreset> allPresets = presetManager.getAllPresets();
        updatePresetList(allPresets);
    }
    
    private void updatePresetList(List<RobotPreset> newPresets) {
        presets.clear();
        presets.addAll(newPresets);
        presetAdapter.notifyDataSetChanged();
        
        // Show/hide empty state
        if (presets.isEmpty()) {
            presetsRecyclerView.setVisibility(View.GONE);
            emptyState.setVisibility(View.VISIBLE);
        } else {
            presetsRecyclerView.setVisibility(View.VISIBLE);
            emptyState.setVisibility(View.GONE);
        }
        
        Log.d(TAG, "Updated preset list with " + presets.size() + " presets");
    }
    
    private void createNewPreset() {
        Intent intent = new Intent(getContext(), PresetEditorActivity.class);
        intent.putExtra(PresetEditorActivity.EXTRA_PRESET_MODE, PresetEditorActivity.MODE_CREATE);
        startActivityForResult(intent, REQUEST_CREATE_PRESET);
    }
    
    private void editPreset(RobotPreset preset) {
        Intent intent = new Intent(getContext(), PresetEditorActivity.class);
        intent.putExtra(PresetEditorActivity.EXTRA_PRESET_MODE, PresetEditorActivity.MODE_EDIT);
        intent.putExtra(PresetEditorActivity.EXTRA_PRESET_ID, preset.getId());
        startActivityForResult(intent, REQUEST_EDIT_PRESET);
    }
    
    private void duplicatePreset(RobotPreset preset) {
        Intent intent = new Intent(getContext(), PresetEditorActivity.class);
        intent.putExtra(PresetEditorActivity.EXTRA_PRESET_MODE, PresetEditorActivity.MODE_DUPLICATE);
        intent.putExtra(PresetEditorActivity.EXTRA_PRESET_ID, preset.getId());
        startActivityForResult(intent, REQUEST_CREATE_PRESET);
    }
    
    private void executePreset(RobotPreset preset) {
        if (presetManager != null) {
            presetManager.executePreset(preset);
        }
    }
    
    private void deletePreset(RobotPreset preset) {
        if (presetManager != null) {
            presetManager.deletePreset(preset.getId());
            loadPresets();
            showMessage("Preset deleted: " + preset.getName());
        }
    }
    
    private void showPresetOptions(RobotPreset preset) {
        // Create a simple dialog with options
        String[] options = {"Execute", "Edit", "Duplicate", "Delete"};
        
        androidx.appcompat.app.AlertDialog.Builder builder = new androidx.appcompat.app.AlertDialog.Builder(getContext());
        builder.setTitle(preset.getName())
                .setItems(options, (dialog, which) -> {
                    switch (which) {
                        case 0: // Execute
                            executePreset(preset);
                            break;
                        case 1: // Edit
                            editPreset(preset);
                            break;
                        case 2: // Duplicate
                            duplicatePreset(preset);
                            break;
                        case 3: // Delete
                            confirmDeletePreset(preset);
                            break;
                    }
                })
                .show();
    }
    
    private void confirmDeletePreset(RobotPreset preset) {
        androidx.appcompat.app.AlertDialog.Builder builder = new androidx.appcompat.app.AlertDialog.Builder(getContext());
        builder.setTitle("Delete Preset")
                .setMessage("Are you sure you want to delete \"" + preset.getName() + "\"?")
                .setPositiveButton("Delete", (dialog, which) -> deletePreset(preset))
                .setNegativeButton("Cancel", null)
                .show();
    }
    
    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        
        if (resultCode == getActivity().RESULT_OK) {
            switch (requestCode) {
                case REQUEST_CREATE_PRESET:
                case REQUEST_EDIT_PRESET:
                    // Reload presets after create/edit
                    loadPresets();
                    break;
            }
        }
    }
    
    private void showMessage(String message) {
        if (getContext() != null) {
            Toast.makeText(getContext(), message, Toast.LENGTH_SHORT).show();
        }
    }
    
    private void showError(String error) {
        if (getContext() != null) {
            Toast.makeText(getContext(), error, Toast.LENGTH_LONG).show();
        }
        Log.e(TAG, error);
    }
    
    @Override
    public void onDestroy() {
        super.onDestroy();
        if (presetManager != null) {
            presetManager.cleanup();
        }
        if (commandParser != null) {
            commandParser.cleanup();
        }
    }
}
