package com.stemrobo.simple.ui;

import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.*;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import com.google.android.material.switchmaterial.SwitchMaterial;
import com.stemrobo.simple.MainActivity;
import com.stemrobo.simple.R;
import com.stemrobo.simple.robot.SimpleRobotController;
import com.stemrobo.simple.vision.SmartGreeting;
import com.stemrobo.simple.utils.LanguageManager;
import java.util.List;

/**
 * Settings Fragment for configuring robot and AI settings
 */
public class SettingsFragment extends Fragment {
    private static final String TAG = "SettingsFragment";
    private static final String PREFS_NAME = "app_settings";

    // Settings Keys
    public static final String PREF_MOVEMENT_FORWARD_DURATION = "movement_forward_duration";
    public static final String PREF_MOVEMENT_SIDE_DURATION = "movement_side_duration";
    public static final String PREF_ESP32_IP = "esp32_ip";
    public static final String PREF_ESP32_PORT = "esp32_port";
    public static final String PREF_ULTRASONIC_ENABLED = "ultrasonic_enabled";
    public static final String PREF_ULTRASONIC_DISTANCE = "ultrasonic_distance";
    public static final String PREF_SMART_GREETING_ENABLED = "smart_greeting_enabled";
    public static final String PREF_GREETING_COOLDOWN = "greeting_cooldown";
    public static final String PREF_SERVO_SPEED = "servo_speed";
    public static final String PREF_WALKING_ANGLE = "walking_angle";

    // Advanced Settings Keys
    public static final String PREF_MIC_SENSITIVITY = "mic_sensitivity";
    public static final String PREF_MIC_DURATION = "mic_duration";
    public static final String PREF_FACE_DETECTION_DURATION = "face_detection_duration";
    public static final String PREF_HANDSHAKE_DURATION = "handshake_duration";
    public static final String PREF_OUT_OF_RANGE_RESET_TIME = "out_of_range_reset_time";
    public static final String PREF_GREETING_DISTANCE_THRESHOLD = "greeting_distance_threshold";

    // Default Values
    public static final int DEFAULT_MIC_SENSITIVITY = 50;
    public static final int DEFAULT_MIC_DURATION = 4000;
    public static final int DEFAULT_FACE_DETECTION_DURATION = 4000;
    public static final int DEFAULT_HANDSHAKE_DURATION = 5000;
    public static final int DEFAULT_OUT_OF_RANGE_RESET_TIME = 10000; // 10 seconds
    public static final int DEFAULT_GREETING_DISTANCE_THRESHOLD = 30;
    public static final int DEFAULT_MOVEMENT_FORWARD_DURATION = 3000;
    public static final int DEFAULT_MOVEMENT_SIDE_DURATION = 2000;
    public static final String DEFAULT_ESP32_IP = "***********";
    public static final int DEFAULT_ESP32_PORT = 80;
    public static final int DEFAULT_ULTRASONIC_DISTANCE = 30;
    public static final int DEFAULT_GREETING_COOLDOWN = 30000;

    // UI Components
    private EditText esp32IpInput;
    private TextView connectionStatusText;
    private Button btnTestConnection;
    
    private SwitchMaterial smartGreetingSwitch;
    private SeekBar greetingDistanceSeekbar;
    private TextView greetingDistanceValue;
    private SeekBar greetingCooldownSeekbar;
    private TextView greetingCooldownValue;
    
    private EditText wakeWordInput;
    private Spinner languageSpinner;
    private RadioGroup voiceGenderGroup;
    private RadioButton voiceFemale;
    private RadioButton voiceMale;

    // AI Settings
    private SeekBar aiContextSizeSeekbar;
    private TextView aiContextSizeValue;
    private Spinner aiPersonalitySpinner;
    private Spinner aiResponseStyleSpinner;
    private SwitchMaterial aiVerboseModeSwitch;
    private Button btnClearAIHistory;

    // Movement Duration Settings
    private SeekBar forwardDurationSeekbar;
    private TextView forwardDurationValue;
    private SeekBar backwardDurationSeekbar;
    private TextView backwardDurationValue;
    private SeekBar leftDurationSeekbar;
    private TextView leftDurationValue;
    private SeekBar rightDurationSeekbar;
    private TextView rightDurationValue;

    // Servo Angle Settings
    private SeekBar handshakeAngleSeekbar;
    private TextView handshakeAngleValue;
    private SeekBar waveAngleSeekbar;
    private TextView waveAngleValue;
    private SeekBar pointAngleSeekbar;
    private TextView pointAngleValue;
    private SeekBar servoSpeedSeekbar;
    private TextView servoSpeedValue;
    private SeekBar walkingAngleSeekbar;
    private TextView walkingAngleValue;

    // Advanced Settings
    private SeekBar micSensitivitySeekbar;
    private TextView micSensitivityValue;
    private SeekBar micDurationSeekbar;
    private TextView micDurationValue;
    private SeekBar faceDetectionDurationSeekbar;
    private TextView faceDetectionDurationValue;
    private SeekBar handshakeDurationSeekbar;
    private TextView handshakeDurationValue;
    private SeekBar outOfRangeResetSeekbar;
    private TextView outOfRangeResetValue;

    private Button btnSaveSettings;

    // System components
    private SimpleRobotController robotController;
    private SmartGreeting smartGreeting;
    private SharedPreferences preferences;
    private LanguageManager languageManager;
    
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_settings, container, false);
        
        initializeViews(view);
        setupListeners();
        
        return view;
    }
    
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        // Get system components from MainActivity
        if (getActivity() instanceof MainActivity) {
            MainActivity mainActivity = (MainActivity) getActivity();
            robotController = mainActivity.getRobotController();
            smartGreeting = mainActivity.getSmartGreeting();
        }
        
        // Initialize preferences
        preferences = getActivity().getSharedPreferences(PREFS_NAME, getActivity().MODE_PRIVATE);

        // Initialize language manager
        languageManager = new LanguageManager(getContext());

        setupLanguageSpinner();
        setupAISettings();
        setupMovementDurationSettings();
        setupServoAngleSettings();
        loadSettings();
    }
    
    private void initializeViews(View view) {
        // Robot settings
        esp32IpInput = view.findViewById(R.id.esp32_ip_input);
        connectionStatusText = view.findViewById(R.id.connection_status_text);
        btnTestConnection = view.findViewById(R.id.btn_test_connection);
        
        // Smart greeting settings
        smartGreetingSwitch = view.findViewById(R.id.smart_greeting_switch);
        greetingDistanceSeekbar = view.findViewById(R.id.greeting_distance_seekbar);
        greetingDistanceValue = view.findViewById(R.id.greeting_distance_value);
        greetingCooldownSeekbar = view.findViewById(R.id.greeting_cooldown_seekbar);
        greetingCooldownValue = view.findViewById(R.id.greeting_cooldown_value);
        
        // AI settings
        wakeWordInput = view.findViewById(R.id.wake_word_input);
        languageSpinner = view.findViewById(R.id.language_spinner);
        voiceGenderGroup = view.findViewById(R.id.voice_gender_group);
        voiceFemale = view.findViewById(R.id.voice_female);
        voiceMale = view.findViewById(R.id.voice_male);

        // Advanced AI settings
        aiContextSizeSeekbar = view.findViewById(R.id.ai_context_size_seekbar);
        aiContextSizeValue = view.findViewById(R.id.ai_context_size_value);
        aiPersonalitySpinner = view.findViewById(R.id.ai_personality_spinner);
        aiResponseStyleSpinner = view.findViewById(R.id.ai_response_style_spinner);
        aiVerboseModeSwitch = view.findViewById(R.id.ai_verbose_mode_switch);
        btnClearAIHistory = view.findViewById(R.id.btn_clear_ai_history);

        // Movement duration settings
        forwardDurationSeekbar = view.findViewById(R.id.forward_duration_seekbar);
        forwardDurationValue = view.findViewById(R.id.forward_duration_value);
        backwardDurationSeekbar = view.findViewById(R.id.backward_duration_seekbar);
        backwardDurationValue = view.findViewById(R.id.backward_duration_value);
        leftDurationSeekbar = view.findViewById(R.id.left_duration_seekbar);
        leftDurationValue = view.findViewById(R.id.left_duration_value);
        rightDurationSeekbar = view.findViewById(R.id.right_duration_seekbar);
        rightDurationValue = view.findViewById(R.id.right_duration_value);

        // Servo angle settings
        handshakeAngleSeekbar = view.findViewById(R.id.handshake_angle_seekbar);
        handshakeAngleValue = view.findViewById(R.id.handshake_angle_value);
        waveAngleSeekbar = view.findViewById(R.id.wave_angle_seekbar);
        waveAngleValue = view.findViewById(R.id.wave_angle_value);
        pointAngleSeekbar = view.findViewById(R.id.point_angle_seekbar);
        pointAngleValue = view.findViewById(R.id.point_angle_value);
        servoSpeedSeekbar = view.findViewById(R.id.servo_speed_seekbar);
        servoSpeedValue = view.findViewById(R.id.servo_speed_value);
        walkingAngleSeekbar = view.findViewById(R.id.walking_angle_seekbar);
        walkingAngleValue = view.findViewById(R.id.walking_angle_value);

        // Advanced Settings
        micSensitivitySeekbar = view.findViewById(R.id.mic_sensitivity_seekbar);
        micSensitivityValue = view.findViewById(R.id.mic_sensitivity_value);
        micDurationSeekbar = view.findViewById(R.id.mic_duration_seekbar);
        micDurationValue = view.findViewById(R.id.mic_duration_value);
        faceDetectionDurationSeekbar = view.findViewById(R.id.face_detection_duration_seekbar);
        faceDetectionDurationValue = view.findViewById(R.id.face_detection_duration_value);
        handshakeDurationSeekbar = view.findViewById(R.id.handshake_duration_seekbar);
        handshakeDurationValue = view.findViewById(R.id.handshake_duration_value);
        outOfRangeResetSeekbar = view.findViewById(R.id.out_of_range_reset_seekbar);
        outOfRangeResetValue = view.findViewById(R.id.out_of_range_reset_value);

        btnSaveSettings = view.findViewById(R.id.btn_save_settings);
    }
    
    private void setupListeners() {
        // Test connection button
        btnTestConnection.setOnClickListener(v -> testConnection());
        
        // Smart greeting switch
        smartGreetingSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (smartGreeting != null) {
                smartGreeting.setGreetingEnabled(isChecked);
            }
            updateGreetingSettingsVisibility(isChecked);
        });
        
        // Greeting distance seekbar
        greetingDistanceSeekbar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                int distance = progress + 10; // 10-110 cm range
                greetingDistanceValue.setText(distance + " cm");
                if (fromUser && smartGreeting != null) {
                    smartGreeting.setDistanceThreshold(distance);
                }
            }
            
            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {}
            
            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {}
        });
        
        // Greeting cooldown seekbar
        greetingCooldownSeekbar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                int cooldown = progress + 5; // 5-65 seconds range
                greetingCooldownValue.setText(cooldown + " seconds");
                if (fromUser && smartGreeting != null) {
                    smartGreeting.setCooldownPeriod(cooldown * 1000L); // Convert to milliseconds
                }
            }
            
            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {}
            
            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {}
        });

        // Advanced Settings Seekbars
        micSensitivitySeekbar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                micSensitivityValue.setText(progress + "%");
            }
            @Override public void onStartTrackingTouch(SeekBar seekBar) {}
            @Override public void onStopTrackingTouch(SeekBar seekBar) {}
        });

        micDurationSeekbar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                float duration = (progress + 5) / 10.0f; // 0.5s to 10.5s
                micDurationValue.setText(String.format("%.1fs", duration));
            }
            @Override public void onStartTrackingTouch(SeekBar seekBar) {}
            @Override public void onStopTrackingTouch(SeekBar seekBar) {}
        });

        faceDetectionDurationSeekbar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                float duration = (progress + 5) / 10.0f; // 0.5s to 10.0s
                faceDetectionDurationValue.setText(String.format("%.1fs", duration));
            }
            @Override public void onStartTrackingTouch(SeekBar seekBar) {}
            @Override public void onStopTrackingTouch(SeekBar seekBar) {}
        });

        handshakeDurationSeekbar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                int duration = progress + 1; // 1s to 30s
                handshakeDurationValue.setText(duration + "s");
            }
            @Override public void onStartTrackingTouch(SeekBar seekBar) {}
            @Override public void onStopTrackingTouch(SeekBar seekBar) {}
        });

        outOfRangeResetSeekbar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                int time = progress + 1; // 1s to 60s
                outOfRangeResetValue.setText(time + "s");
            }
            @Override public void onStartTrackingTouch(SeekBar seekBar) {}
            @Override public void onStopTrackingTouch(SeekBar seekBar) {}
        });

        // Save settings button
        btnSaveSettings.setOnClickListener(v -> saveSettings());

        // Servo speed seekbar
        servoSpeedSeekbar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                servoSpeedValue.setText(progress + "%");
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {}

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {}
        });

        // Walking angle seekbar
        walkingAngleSeekbar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                walkingAngleValue.setText(progress + "°");
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {}

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {}
        });
    }
    
    private void loadSettings() {
        // Load robot settings
        if (robotController != null) {
            String esp32IP = robotController.getESP32IP();
            esp32IpInput.setText(esp32IP);
            updateConnectionStatus();
        }
        
        // Load smart greeting settings
        if (smartGreeting != null) {
            smartGreetingSwitch.setChecked(smartGreeting.isGreetingEnabled());
            
            float distance = smartGreeting.getDistanceThreshold();
            greetingDistanceSeekbar.setProgress((int) distance - 10);
            greetingDistanceValue.setText((int) distance + " cm");
            
            long cooldownMs = smartGreeting.getCooldownPeriod();
            int cooldownSec = (int) (cooldownMs / 1000);
            greetingCooldownSeekbar.setProgress(cooldownSec - 5);
            greetingCooldownValue.setText(cooldownSec + " seconds");
            
            updateGreetingSettingsVisibility(smartGreeting.isGreetingEnabled());
        }
        
        // Load AI settings
        String wakeWord = preferences.getString("wake_word", "hey robot");
        wakeWordInput.setText(wakeWord);
        
        boolean isMaleVoice = preferences.getBoolean("voice_male", false);
        if (isMaleVoice) {
            voiceMale.setChecked(true);
        } else {
            voiceFemale.setChecked(true);
        }

        // Load movement duration settings
        int forwardDuration = preferences.getInt("movement_forward_duration", 3);
        int backwardDuration = preferences.getInt("movement_backward_duration", 3);
        int leftDuration = preferences.getInt("movement_left_duration", 2);
        int rightDuration = preferences.getInt("movement_right_duration", 2);

        forwardDurationSeekbar.setProgress(forwardDuration);
        forwardDurationValue.setText(forwardDuration + "s");
        backwardDurationSeekbar.setProgress(backwardDuration);
        backwardDurationValue.setText(backwardDuration + "s");
        leftDurationSeekbar.setProgress(leftDuration);
        leftDurationValue.setText(leftDuration + "s");
        rightDurationSeekbar.setProgress(rightDuration);
        rightDurationValue.setText(rightDuration + "s");

        // Load servo angle settings
        int handshakeAngle = preferences.getInt("servo_handshake_angle", 90);
        int waveAngle = preferences.getInt("servo_wave_angle", 120);
        int pointAngle = preferences.getInt("servo_point_angle", 180);

        handshakeAngleSeekbar.setProgress(handshakeAngle);
        handshakeAngleValue.setText(handshakeAngle + "°");
        waveAngleSeekbar.setProgress(waveAngle);
        waveAngleValue.setText(waveAngle + "°");
        pointAngleSeekbar.setProgress(pointAngle);
        pointAngleValue.setText(pointAngle + "°");

        int walkingAngle = preferences.getInt(PREF_WALKING_ANGLE, 90);
        walkingAngleSeekbar.setProgress(walkingAngle);
        walkingAngleValue.setText(walkingAngle + "°");

        int servoSpeed = preferences.getInt(PREF_SERVO_SPEED, 50);
        servoSpeedSeekbar.setProgress(servoSpeed);
        servoSpeedValue.setText(servoSpeed + "%");

        // Load advanced settings
        int micSensitivity = preferences.getInt(PREF_MIC_SENSITIVITY, DEFAULT_MIC_SENSITIVITY);
        micSensitivitySeekbar.setProgress(micSensitivity);
        micSensitivityValue.setText(micSensitivity + "%");

        int micDuration = preferences.getInt(PREF_MIC_DURATION, DEFAULT_MIC_DURATION);
        micDurationSeekbar.setProgress((micDuration / 100) - 5); // Convert to seekbar range
        micDurationValue.setText(String.format("%.1fs", micDuration / 1000.0f));

        int faceDetectionDuration = preferences.getInt(PREF_FACE_DETECTION_DURATION, DEFAULT_FACE_DETECTION_DURATION);
        faceDetectionDurationSeekbar.setProgress((faceDetectionDuration / 100) - 5); // Convert to seekbar range
        faceDetectionDurationValue.setText(String.format("%.1fs", faceDetectionDuration / 1000.0f));

        int handshakeDuration = preferences.getInt(PREF_HANDSHAKE_DURATION, DEFAULT_HANDSHAKE_DURATION);
        handshakeDurationSeekbar.setProgress((handshakeDuration / 1000) - 1); // Convert to seekbar range
        handshakeDurationValue.setText((handshakeDuration / 1000) + "s");

        int outOfRangeResetTime = preferences.getInt(PREF_OUT_OF_RANGE_RESET_TIME, DEFAULT_OUT_OF_RANGE_RESET_TIME);
        outOfRangeResetSeekbar.setProgress((outOfRangeResetTime / 1000) - 1); // Convert to seekbar range
        outOfRangeResetValue.setText((outOfRangeResetTime / 1000) + "s");
    }
    
    private void saveSettings() {
        try {
            // Save robot settings
            String esp32IP = esp32IpInput.getText().toString().trim();
            if (!esp32IP.isEmpty() && robotController != null) {
                robotController.updateESP32IP(esp32IP);
            }
            
            // Save AI settings
            String wakeWord = wakeWordInput.getText().toString().trim();
            if (!wakeWord.isEmpty()) {
                preferences.edit().putString("wake_word", wakeWord).apply();
            }
            
            boolean isMaleVoice = voiceMale.isChecked();
            preferences.edit().putBoolean("voice_male", isMaleVoice).apply();

            // Save movement duration settings
            int forwardDuration = Math.max(1, forwardDurationSeekbar.getProgress());
            int backwardDuration = Math.max(1, backwardDurationSeekbar.getProgress());
            int leftDuration = Math.max(1, leftDurationSeekbar.getProgress());
            int rightDuration = Math.max(1, rightDurationSeekbar.getProgress());

            preferences.edit()
                .putInt("movement_forward_duration", forwardDuration)
                .putInt("movement_backward_duration", backwardDuration)
                .putInt("movement_left_duration", leftDuration)
                .putInt("movement_right_duration", rightDuration)
                .apply();

            // Save servo angle settings
            int handshakeAngle = handshakeAngleSeekbar.getProgress();
            int waveAngle = waveAngleSeekbar.getProgress();
            int pointAngle = pointAngleSeekbar.getProgress();

            preferences.edit()
                .putInt("servo_handshake_angle", handshakeAngle)
                .putInt("servo_wave_angle", waveAngle)
                .putInt("servo_point_angle", pointAngle)
                .putInt(PREF_WALKING_ANGLE, walkingAngleSeekbar.getProgress())
                .putInt(PREF_SERVO_SPEED, servoSpeedSeekbar.getProgress())
                .apply();

            // Save advanced settings
            int micSensitivity = micSensitivitySeekbar.getProgress();
            int micDuration = (micDurationSeekbar.getProgress() + 5) * 100; // Convert to milliseconds
            int faceDetectionDuration = (faceDetectionDurationSeekbar.getProgress() + 5) * 100; // Convert to milliseconds
            int handshakeDuration = (handshakeDurationSeekbar.getProgress() + 1) * 1000; // Convert to milliseconds
            int outOfRangeResetTime = (outOfRangeResetSeekbar.getProgress() + 1) * 1000; // Convert to milliseconds

            preferences.edit()
                .putInt(PREF_MIC_SENSITIVITY, micSensitivity)
                .putInt(PREF_MIC_DURATION, micDuration)
                .putInt(PREF_FACE_DETECTION_DURATION, faceDetectionDuration)
                .putInt(PREF_HANDSHAKE_DURATION, handshakeDuration)
                .putInt(PREF_OUT_OF_RANGE_RESET_TIME, outOfRangeResetTime)
                .apply();

            // Update SmartGreeting with new settings
            if (smartGreeting != null) {
                smartGreeting.setFaceDetectionDuration(faceDetectionDuration);
                smartGreeting.setHandshakeDuration(handshakeDuration);
                smartGreeting.setOutOfRangeResetTime(outOfRangeResetTime);
            }

            if (robotController != null) {
                robotController.setServoSpeed(servoSpeedSeekbar.getProgress());
                robotController.setWalkingAngle(walkingAngleSeekbar.getProgress());
            }

            // Update language across all services
            updateLanguageServices();

            showMessage("Settings saved successfully");
            
        } catch (Exception e) {
            showError("Failed to save settings: " + e.getMessage());
        }
    }
    
    private void testConnection() {
        if (robotController != null) {
            connectionStatusText.setText("Testing connection...");
            connectionStatusText.setTextColor(getResources().getColor(R.color.status_connecting, null));
            
            robotController.testConnection();
            
            // Update status after a delay
            new android.os.Handler().postDelayed(this::updateConnectionStatus, 2000);
        } else {
            showError("Robot controller not available");
        }
    }
    
    private void updateConnectionStatus() {
        if (robotController != null) {
            boolean isConnected = robotController.isConnected();
            if (isConnected) {
                connectionStatusText.setText("Robot Online");
                connectionStatusText.setTextColor(getResources().getColor(R.color.status_online, null));
            } else {
                connectionStatusText.setText("Robot Offline");
                connectionStatusText.setTextColor(getResources().getColor(R.color.status_offline, null));
            }
        }
    }
    
    private void setupLanguageSpinner() {
        List<LanguageManager.Language> languages = LanguageManager.getSupportedLanguages();
        ArrayAdapter<LanguageManager.Language> adapter = new ArrayAdapter<>(
            getContext(),
            android.R.layout.simple_spinner_item,
            languages
        );
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        languageSpinner.setAdapter(adapter);

        // Set current language
        String currentLanguage = languageManager.getCurrentLanguage();
        for (int i = 0; i < languages.size(); i++) {
            if (languages.get(i).code.equals(currentLanguage)) {
                languageSpinner.setSelection(i);
                break;
            }
        }

        // Set listener for language changes
        languageSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                LanguageManager.Language selectedLanguage = languages.get(position);
                if (!selectedLanguage.code.equals(languageManager.getCurrentLanguage())) {
                    languageManager.setLanguage(selectedLanguage.code);
                    // Restart activity to apply language change
                    if (getActivity() != null) {
                        getActivity().recreate();
                    }
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {}
        });
    }

    private void updateGreetingSettingsVisibility(boolean enabled) {
        int visibility = enabled ? View.VISIBLE : View.GONE;
        greetingDistanceSeekbar.setVisibility(visibility);
        greetingDistanceValue.setVisibility(visibility);
        greetingCooldownSeekbar.setVisibility(visibility);
        greetingCooldownValue.setVisibility(visibility);

        // Labels are part of the seekbar sections, no need to hide them separately
    }
    
    private void showMessage(String message) {
        if (getContext() != null) {
            Toast.makeText(getContext(), message, Toast.LENGTH_SHORT).show();
        }
    }
    
    private void showError(String error) {
        if (getContext() != null) {
            Toast.makeText(getContext(), "Error: " + error, Toast.LENGTH_LONG).show();
        }
    }

    private void setupAISettings() {
        // Setup AI context size seekbar
        aiContextSizeSeekbar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                int value = Math.max(1, progress); // Minimum 1
                aiContextSizeValue.setText(String.valueOf(value));
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {}

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {}
        });

        // Setup AI personality spinner
        String[] personalities = {
            getString(R.string.personality_helpful_assistant),
            getString(R.string.personality_friendly_companion),
            getString(R.string.personality_professional_tutor),
            getString(R.string.personality_playful_friend)
        };
        ArrayAdapter<String> personalityAdapter = new ArrayAdapter<>(
            getContext(),
            android.R.layout.simple_spinner_item,
            personalities
        );
        personalityAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        aiPersonalitySpinner.setAdapter(personalityAdapter);

        // Setup AI response style spinner
        String[] responseStyles = {
            getString(R.string.style_conversational),
            getString(R.string.style_formal),
            getString(R.string.style_casual),
            getString(R.string.style_educational)
        };
        ArrayAdapter<String> styleAdapter = new ArrayAdapter<>(
            getContext(),
            android.R.layout.simple_spinner_item,
            responseStyles
        );
        styleAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        aiResponseStyleSpinner.setAdapter(styleAdapter);

        // Setup clear AI history button
        btnClearAIHistory.setOnClickListener(v -> {
            // Clear AI conversation history
            MainActivity mainActivity = (MainActivity) getActivity();
            if (mainActivity != null) {
                // TODO: Access AI service and clear history
                Toast.makeText(getContext(), "AI history cleared", Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void setupMovementDurationSettings() {
        // Setup forward duration seekbar
        forwardDurationSeekbar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                int value = Math.max(1, progress); // Minimum 1 second
                forwardDurationValue.setText(value + "s");
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {}

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {}
        });

        // Setup backward duration seekbar
        backwardDurationSeekbar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                int value = Math.max(1, progress); // Minimum 1 second
                backwardDurationValue.setText(value + "s");
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {}

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {}
        });

        // Setup left duration seekbar
        leftDurationSeekbar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                int value = Math.max(1, progress); // Minimum 1 second
                leftDurationValue.setText(value + "s");
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {}

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {}
        });

        // Setup right duration seekbar
        rightDurationSeekbar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                int value = Math.max(1, progress); // Minimum 1 second
                rightDurationValue.setText(value + "s");
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {}

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {}
        });
    }

    private void setupServoAngleSettings() {
        // Setup handshake angle seekbar
        handshakeAngleSeekbar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                handshakeAngleValue.setText(progress + "°");
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {}

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {}
        });

        // Setup wave angle seekbar
        waveAngleSeekbar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                waveAngleValue.setText(progress + "°");
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {}

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {}
        });

        // Setup point angle seekbar
        pointAngleSeekbar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                pointAngleValue.setText(progress + "°");
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {}

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {}
        });
    }

    @Override
    public void onResume() {
        super.onResume();
        updateConnectionStatus();
    }

    /**
     * Update language settings across all services
     */
    private void updateLanguageServices() {
        try {
            // Language services will automatically use the new language setting:
            // - VoiceService uses LanguageManager for speech recognition locale
            // - TTSService uses LanguageManager for TTS locale
            // - GeminiAIService uses LanguageManager for system prompts and language instructions
            // - Command translation happens automatically in AIFragment

            android.util.Log.d(TAG, "Language updated - services will use new language on next interaction");
            showMessage("Language updated successfully. New language will be used for voice recognition, TTS, and AI responses.");
        } catch (Exception e) {
            android.util.Log.e(TAG, "Error updating language services", e);
        }
    }
}
