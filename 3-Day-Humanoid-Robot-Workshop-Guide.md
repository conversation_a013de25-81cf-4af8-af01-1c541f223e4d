# 3-Day Humanoid Robot Workshop for School Students
## Building Your First Humanoid Robot from Scratch

### 🎯 Workshop Overview
**Target Audience:** School students (Ages 12-18)  
**Duration:** 3 days (6 hours per day, 18 hours total)  
**Skill Level:** Beginner to Intermediate  
**Class Size:** Maximum 20 students (2 students per robot kit)

### 🤖 What Students Will Build
By the end of this workshop, each team will have created a fully functional humanoid robot with:
- **Movement System**: Two-wheel differential drive for forward, backward, left, and right movement
- **Head Control**: Pan (left/right) and tilt (up/down) movement using servo motors
- **Arm Movement**: Left and right arm servo control with gesture capabilities (wave, point, rest)
- **Smartphone Control**: Web-based interface for wireless robot control via WiFi
- **Smart Features**: Distance sensing, voice interaction, and basic AI responses
- **Safety Features**: Emergency stop, battery monitoring, and status indicators

### 📋 Complete Hardware Components List

#### Core Electronics (Per Robot Kit)
- 1x ESP32 DevKit V1 microcontroller board
- 1x Breadboard (830 tie-points) or custom PCB
- 1x USB-C cable for programming and power
- 1x 7.4V Li-Po battery pack (2000mAh) with protection circuit
- 1x Battery connector with ON/OFF switch
- 1x Voltage regulator module (7.4V to 5V/3.3V)

#### Motors & Actuators
- 2x DC gear motors (6V, 200 RPM) with wheels and mounting brackets
- 1x L298N dual motor driver module
- 2x MG90S servo motors (for head pan and tilt)
- 2x SG90 servo motors (for left and right arms)
- 4x Servo mounting brackets and hardware

#### Sensors & Feedback
- 1x HC-SR04 ultrasonic distance sensor
- 2x Status LEDs (red and green)
- 1x Buzzer for audio feedback
- 1x Push button for emergency stop

#### Mechanical Structure
- 1x Robot chassis (laser-cut acrylic or 3D printed)
- 1x Head assembly with servo mounts
- 2x Arm assemblies with servo attachments
- Assorted screws, nuts, and standoffs
- Cable management ties and clips

#### Wiring & Connections
- 20x Male-to-male jumper wires
- 20x Male-to-female jumper wires
- 1x Soldering kit (iron, solder, flux)
- 1x Wire strippers and cutters
- 1x Multimeter for testing

### 🛡️ Safety Guidelines & Workshop Rules
- Always disconnect power when making wiring changes
- Handle electronic components with care - avoid static electricity
- Wear safety glasses during soldering activities
- Keep workspace clean and organized
- Ask instructors for help when uncertain
- No food or drinks near electronic components
- Emergency stop procedures must be understood by all students

## 📅 Day-by-Day Workshop Schedule

### Day 1: Foundation & Assembly (6 hours)
**Learning Objectives:**
- Understand basic robotics concepts and components
- Learn about microcontrollers and the ESP32 platform
- Master basic wiring and circuit construction
- Program first robot behaviors

**Morning Session (3 hours):**

**Hour 1: Introduction to Robotics & Components**
- Welcome and safety briefing
- Introduction to humanoid robotics concepts
- Component identification and function explanation
- Understanding the ESP32 microcontroller
- Overview of the complete robot system

**Hour 2: Mechanical Assembly**
- Chassis assembly and motor mounting
- Installing wheels and testing mechanical movement
- Head assembly with servo mounting
- Arm assembly and attachment points
- Cable routing and management planning

**Hour 3: Basic Electronics & Wiring**
- Understanding breadboard connections
- Power distribution and voltage regulation
- Motor driver wiring and connections
- LED status indicators installation
- Basic multimeter usage and circuit testing

**Afternoon Session (3 hours):**

**Hour 4: ESP32 Programming Setup**
- Arduino IDE installation and configuration
- ESP32 board package installation
- USB driver setup and connection testing
- First program: LED blink test
- Serial monitor introduction and debugging

**Hour 5: Motor Control Programming**
- Understanding PWM and digital control
- Programming basic movement functions
- Testing forward, backward, left, right movements
- Implementing stop and emergency brake functions
- Motor speed control and fine-tuning

**Hour 6: Servo Control Implementation**
- Servo motor theory and control signals
- Programming head pan and tilt movements
- Implementing arm control functions
- Creating smooth movement algorithms
- Testing all mechanical systems

**Day 1 Deliverables:**
- Fully assembled robot chassis with working motors
- Functional head and arm servo control
- Basic movement programming completed
- All safety systems operational

### Day 2: Advanced Control & Communication (6 hours)
**Learning Objectives:**
- Implement wireless communication systems
- Create smartphone control interface
- Add sensor integration and feedback
- Develop gesture and behavior programming

**Morning Session (3 hours):**

**Hour 1: WiFi Communication Setup**
- ESP32 WiFi capabilities overview
- Access Point mode configuration
- Web server implementation
- HTTP request handling
- Network troubleshooting techniques

**Hour 2: Smartphone Control Interface**
- HTML/CSS web interface design
- JavaScript control functions
- Touch-friendly button layouts
- Real-time command sending
- Connection status monitoring

**Hour 3: Sensor Integration**
- Ultrasonic sensor wiring and programming
- Distance measurement and calibration
- Obstacle detection algorithms
- Sensor data filtering and processing
- Safety distance implementation

**Afternoon Session (3 hours):**

**Hour 4: Gesture Programming**
- Creating wave gesture sequence
- Programming pointing behavior
- Rest position implementation
- Smooth movement interpolation
- Gesture timing and coordination

**Hour 5: Smart Behaviors**
- Proximity-based greeting system
- Automatic head tracking simulation
- Voice response integration
- Behavior state management
- Emergency response protocols

**Hour 6: System Integration & Testing**
- Complete system testing
- Wireless range testing
- Battery life optimization
- Performance tuning
- Troubleshooting common issues

**Day 2 Deliverables:**
- Fully functional wireless control system
- Working smartphone interface
- Integrated sensor feedback
- Complete gesture library
- Robust communication protocols

### Day 3: AI Integration & Showcase Preparation (6 hours)
**Learning Objectives:**
- Integrate AI and voice capabilities
- Implement advanced robot behaviors
- Prepare demonstration sequences
- Document and present projects

**Morning Session (3 hours):**

**Hour 1: AI Integration Basics**
- Introduction to AI in robotics
- Voice recognition setup
- Text-to-speech implementation
- Simple conversation flows
- AI response programming

**Hour 2: Advanced Behaviors**
- Smart greeting sequences
- Person detection responses
- Interactive conversation modes
- Autonomous behavior patterns
- Learning and adaptation concepts

**Hour 3: Customization & Personalization**
- Custom gesture creation
- Personality programming
- Voice customization options
- Behavior modification tools
- Student creative input implementation

**Afternoon Session (3 hours):**

**Hour 4: Performance Optimization**
- Code optimization techniques
- Battery life maximization
- Response time improvement
- Memory usage optimization
- System reliability enhancement

**Hour 5: Showcase Preparation**
- Demonstration sequence planning
- Presentation skills training
- Robot performance rehearsal
- Documentation completion
- Video recording preparation

**Hour 6: Final Presentations & Celebration**
- Student team presentations (10 minutes each)
- Robot demonstration sessions
- Peer evaluation and feedback
- Awards and recognition ceremony
- Future learning pathway discussion

**Day 3 Deliverables:**
- AI-enhanced robot with voice capabilities
- Polished demonstration sequences
- Complete project documentation
- Presentation materials
- Celebration of achievements

## 🔧 Technical Implementation Details

### ESP32 Pin Configuration
```
Motor Control:
- Pin 25, 26: Left motor (via L298N driver)
- Pin 14, 15: Right motor (via L298N driver)

Servo Control:
- Pin 18: Head pan servo (left/right rotation)
- Pin 19: Head tilt servo (up/down rotation)
- Pin 33: Left arm servo
- Pin 5: Right arm servo

Sensors & Feedback:
- Pin 23: Ultrasonic trigger
- Pin 22: Ultrasonic echo
- Pin 2: Status LED (built-in)
- Pin 4: External status LED
- Pin 21: Emergency stop button
- Pin 27: Buzzer
```

### Core Programming Concepts Taught
1. **Microcontroller Programming**: Arduino C++ basics
2. **Motor Control**: PWM signals and H-bridge drivers
3. **Servo Control**: Pulse width modulation for precise positioning
4. **Wireless Communication**: WiFi protocols and web servers
5. **Sensor Integration**: Digital and analog sensor reading
6. **State Machines**: Behavior programming and decision making
7. **User Interface Design**: Web-based control interfaces
8. **System Integration**: Combining multiple subsystems

### Safety & Emergency Protocols
- Emergency stop button immediately halts all movement
- Battery voltage monitoring prevents over-discharge
- Thermal protection for motor drivers
- Wireless connection timeout safety
- Physical limit switches for servo protection
- Clear emergency procedures posted in workshop

## 🎓 Learning Outcomes & Skills Developed

### Technical Skills
- Basic electronics and circuit construction
- Microcontroller programming and debugging
- Mechanical assembly and troubleshooting
- Wireless communication protocols
- Sensor integration and data processing
- User interface design principles

### Soft Skills
- Problem-solving and critical thinking
- Teamwork and collaboration
- Project management and time planning
- Presentation and communication skills
- Creative thinking and innovation
- Persistence and debugging mindset

### STEM Concepts Reinforced
- Physics: Motors, sensors, mechanical systems
- Mathematics: Geometry, trigonometry, algorithms
- Computer Science: Programming, networking, AI basics
- Engineering: Design process, testing, optimization

## 📚 Post-Workshop Resources & Next Steps

### Continued Learning Opportunities
- Advanced robotics workshops (Level 2 & 3)
- Robotics competition team participation
- Online programming courses and tutorials
- Maker space access for continued development
- Mentorship programs with industry professionals

### Project Extension Ideas
- Add camera for computer vision
- Implement voice recognition
- Create mobile app control
- Add more sensors (temperature, light, sound)
- Develop autonomous navigation
- Build robot-to-robot communication

### Career Pathway Information
- Robotics engineering opportunities
- Computer science and programming careers
- Mechanical and electrical engineering paths
- AI and machine learning specializations
- Entrepreneurship in technology sector

## 💻 Programming Code Examples

### Basic Robot Movement Code
```cpp
// Basic movement functions for Day 1
void moveForward() {
    digitalWrite(MOTOR_LEFT_1, HIGH);
    digitalWrite(MOTOR_LEFT_2, LOW);
    digitalWrite(MOTOR_RIGHT_1, HIGH);
    digitalWrite(MOTOR_RIGHT_2, LOW);
    Serial.println("Moving Forward");
}

void moveBackward() {
    digitalWrite(MOTOR_LEFT_1, LOW);
    digitalWrite(MOTOR_LEFT_2, HIGH);
    digitalWrite(MOTOR_RIGHT_1, LOW);
    digitalWrite(MOTOR_RIGHT_2, HIGH);
    Serial.println("Moving Backward");
}

void turnLeft() {
    digitalWrite(MOTOR_LEFT_1, LOW);
    digitalWrite(MOTOR_LEFT_2, HIGH);
    digitalWrite(MOTOR_RIGHT_1, HIGH);
    digitalWrite(MOTOR_RIGHT_2, LOW);
    Serial.println("Turning Left");
}

void turnRight() {
    digitalWrite(MOTOR_LEFT_1, HIGH);
    digitalWrite(MOTOR_LEFT_2, LOW);
    digitalWrite(MOTOR_RIGHT_1, LOW);
    digitalWrite(MOTOR_RIGHT_2, HIGH);
    Serial.println("Turning Right");
}

void stopRobot() {
    digitalWrite(MOTOR_LEFT_1, LOW);
    digitalWrite(MOTOR_LEFT_2, LOW);
    digitalWrite(MOTOR_RIGHT_1, LOW);
    digitalWrite(MOTOR_RIGHT_2, LOW);
    Serial.println("Robot Stopped");
}
```

### Servo Control Code
```cpp
// Servo control for head and arms
#include <ESP32Servo.h>

Servo headPanServo;
Servo headTiltServo;
Servo leftArmServo;
Servo rightArmServo;

void setupServos() {
    headPanServo.attach(18);
    headTiltServo.attach(19);
    leftArmServo.attach(33);
    rightArmServo.attach(5);

    // Set initial positions
    headPanServo.write(90);    // Center
    headTiltServo.write(90);   // Center
    leftArmServo.write(180);   // Rest position
    rightArmServo.write(0);    // Rest position
}

void waveGesture() {
    // Wave with right arm
    for(int i = 0; i <= 90; i += 5) {
        rightArmServo.write(i);
        delay(50);
    }
    for(int i = 90; i >= 0; i -= 5) {
        rightArmServo.write(i);
        delay(50);
    }
    Serial.println("Wave gesture completed");
}

void lookLeft() {
    headPanServo.write(45);
    Serial.println("Looking left");
}

void lookRight() {
    headPanServo.write(135);
    Serial.println("Looking right");
}

void centerHead() {
    headPanServo.write(90);
    headTiltServo.write(90);
    Serial.println("Head centered");
}
```

### WiFi Web Server Code
```cpp
// WiFi setup for smartphone control
#include <WiFi.h>
#include <WebServer.h>

const char* ssid = "ROBO_WORKSHOP";
const char* password = "123456789";

WebServer server(80);

void setupWiFi() {
    WiFi.softAP(ssid, password);
    Serial.println("WiFi Access Point started");
    Serial.print("IP address: ");
    Serial.println(WiFi.softAPIP());

    server.on("/", handleRoot);
    server.on("/move", handleMove);
    server.on("/servo", handleServo);
    server.begin();
    Serial.println("Web server started");
}

void handleRoot() {
    String html = R"(
    <!DOCTYPE html>
    <html>
    <head>
        <title>Robot Control</title>
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <style>
            body { font-family: Arial; text-align: center; margin: 20px; }
            .button {
                background-color: #4CAF50;
                border: none;
                color: white;
                padding: 15px 32px;
                text-align: center;
                font-size: 16px;
                margin: 4px 2px;
                cursor: pointer;
                border-radius: 8px;
                width: 120px;
            }
            .stop { background-color: #f44336; }
            .servo { background-color: #2196F3; }
        </style>
    </head>
    <body>
        <h1>🤖 Humanoid Robot Control</h1>
        <h2>Movement Controls</h2>
        <button class="button" onclick="sendCommand('F')">Forward</button><br>
        <button class="button" onclick="sendCommand('L')">Left</button>
        <button class="button stop" onclick="sendCommand('S')">STOP</button>
        <button class="button" onclick="sendCommand('R')">Right</button><br>
        <button class="button" onclick="sendCommand('B')">Backward</button><br>

        <h2>Head Controls</h2>
        <button class="button servo" onclick="sendServo('LOOK_LEFT')">Look Left</button>
        <button class="button servo" onclick="sendServo('CENTER_HEAD')">Center</button>
        <button class="button servo" onclick="sendServo('LOOK_RIGHT')">Look Right</button><br>

        <h2>Gestures</h2>
        <button class="button servo" onclick="sendServo('WAVE')">Wave</button>
        <button class="button servo" onclick="sendServo('POINT')">Point</button>
        <button class="button servo" onclick="sendServo('REST')">Rest</button>

        <script>
            function sendCommand(cmd) {
                fetch('/move?cmd=' + cmd);
            }
            function sendServo(cmd) {
                fetch('/servo?cmd=' + cmd);
            }
        </script>
    </body>
    </html>
    )";
    server.send(200, "text/html", html);
}

void handleMove() {
    String command = server.arg("cmd");
    if(command == "F") moveForward();
    else if(command == "B") moveBackward();
    else if(command == "L") turnLeft();
    else if(command == "R") turnRight();
    else if(command == "S") stopRobot();

    server.send(200, "text/plain", "OK");
}

void handleServo() {
    String command = server.arg("cmd");
    if(command == "WAVE") waveGesture();
    else if(command == "LOOK_LEFT") lookLeft();
    else if(command == "LOOK_RIGHT") lookRight();
    else if(command == "CENTER_HEAD") centerHead();

    server.send(200, "text/plain", "OK");
}
```

## 🧪 Hands-On Activities & Experiments

### Day 1 Activities

**Activity 1.1: Component Scavenger Hunt (30 minutes)**
- Students identify and sort all components
- Learn component names and functions
- Practice using multimeter for basic measurements
- Create component inventory checklist

**Activity 1.2: LED Circuit Challenge (45 minutes)**
- Build simple LED circuits with different patterns
- Learn about current limiting resistors
- Implement blinking patterns with different timing
- Troubleshoot common wiring mistakes

**Activity 1.3: Motor Testing Lab (60 minutes)**
- Test individual motors with battery power
- Measure motor current and voltage
- Experiment with different speeds using PWM
- Document motor performance characteristics

### Day 2 Activities

**Activity 2.1: Servo Precision Challenge (45 minutes)**
- Program servos to move to exact angles
- Create smooth movement sequences
- Implement position feedback and correction
- Design custom gesture sequences

**Activity 2.2: Wireless Range Testing (30 minutes)**
- Test WiFi connection at different distances
- Measure signal strength and response time
- Identify interference sources and solutions
- Optimize antenna positioning

**Activity 2.3: Sensor Calibration Lab (60 minutes)**
- Calibrate ultrasonic sensor accuracy
- Test sensor performance in different conditions
- Implement noise filtering algorithms
- Create distance-based behavior triggers

### Day 3 Activities

**Activity 3.1: AI Conversation Design (45 minutes)**
- Create personality profiles for robots
- Design conversation flows and responses
- Implement voice recognition triggers
- Test different interaction scenarios

**Activity 3.2: Behavior Programming Challenge (60 minutes)**
- Program complex behavior sequences
- Implement state machine logic
- Create autonomous decision-making algorithms
- Test robot responses to different stimuli

**Activity 3.3: Robot Olympics (90 minutes)**
- Obstacle course navigation challenge
- Precision movement competitions
- Gesture accuracy contests
- Creative demonstration showcase

## 🔧 Troubleshooting Guide

### Common Hardware Issues

**Motors Not Working:**
- Check power connections and battery voltage
- Verify motor driver wiring and enable pins
- Test motors directly with battery
- Check for loose connections or damaged wires

**Servos Not Responding:**
- Verify servo power supply (5V required)
- Check PWM signal connections
- Test servo with simple sweep program
- Ensure servo library is properly included

**WiFi Connection Problems:**
- Check ESP32 antenna positioning
- Verify network credentials and settings
- Test with different devices and browsers
- Monitor serial output for error messages

**Sensor Reading Errors:**
- Check sensor power and ground connections
- Verify trigger and echo pin assignments
- Test sensor with simple distance program
- Check for physical obstructions or interference

### Common Software Issues

**Upload Errors:**
- Verify correct board and port selection
- Check USB cable and driver installation
- Try different USB ports or cables
- Reset ESP32 before uploading

**Compilation Errors:**
- Check library installations and versions
- Verify syntax and bracket matching
- Ensure all required includes are present
- Check variable declarations and scope

**Runtime Errors:**
- Monitor serial output for debug information
- Check for infinite loops or blocking code
- Verify memory usage and stack overflow
- Test individual functions separately

## 📊 Assessment & Evaluation

### Daily Assessment Criteria

**Day 1 Assessment (Technical Foundation):**
- Successful component identification (20%)
- Proper wiring and connections (30%)
- Basic programming implementation (30%)
- Safety protocol adherence (20%)

**Day 2 Assessment (Integration & Communication):**
- Wireless communication setup (25%)
- Sensor integration and calibration (25%)
- User interface functionality (25%)
- System integration and testing (25%)

**Day 3 Assessment (Innovation & Presentation):**
- AI integration and customization (30%)
- Creative problem-solving approach (25%)
- Presentation quality and clarity (25%)
- Teamwork and collaboration (20%)

### Final Project Evaluation Rubric

**Technical Implementation (40%):**
- All systems functional and integrated
- Code quality and organization
- Hardware assembly and wiring
- Performance optimization and reliability

**Innovation & Creativity (30%):**
- Unique features or customizations
- Creative problem-solving approaches
- Original gesture or behavior design
- Aesthetic and design considerations

**Documentation & Presentation (20%):**
- Clear project documentation
- Effective demonstration and explanation
- Understanding of underlying concepts
- Communication skills and confidence

**Teamwork & Process (10%):**
- Effective collaboration and communication
- Equal participation and contribution
- Problem-solving approach and persistence
- Adherence to safety protocols

### Certification & Recognition

**Workshop Completion Certificate:**
- Awarded to all students completing 80% of activities
- Includes skill competencies achieved
- Signed by instructors and STEM-Xpert team
- Digital badge for online portfolios

**Excellence Awards:**
- Most Innovative Design
- Best Technical Implementation
- Outstanding Teamwork
- Creative Problem Solver
- Best Presentation

---

**Workshop Developed By:** STEM-Xpert Education Team
**Version:** 2.0
**Last Updated:** 2024
**Contact:** <EMAIL>
**Website:** www.stem-xpert.com/robotics-workshops

**Special Thanks:** To all the students, educators, and industry partners who helped develop and refine this comprehensive robotics education program.
