package com.stemrobo.simple.robot;

import android.util.Log;
import okhttp3.*;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * Simplified ESP32 Communication Manager
 * Handles WiFi HTTP communication with ESP32
 * Supports basic commands and sensor readings
 */
public class ESP32SimpleComm {
    private static final String TAG = "ESP32SimpleComm";
    private static final int ESP32_PORT = 80;
    private static final int CONNECTION_TIMEOUT = 5; // seconds
    private static final int READ_TIMEOUT = 10; // seconds
    
    private String esp32IP;
    private OkHttpClient httpClient;
    private ConnectionCallback connectionCallback;
    
    public interface ConnectionCallback {
        void onConnectionStatusChanged(boolean connected, String message);
    }
    
    public ESP32SimpleComm(String esp32IP) {
        this.esp32IP = esp32IP;
        initializeHttpClient();
    }
    
    private void initializeHttpClient() {
        httpClient = new OkHttpClient.Builder()
            .connectTimeout(CONNECTION_TIMEOUT, TimeUnit.SECONDS)
            .readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
            .writeTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
            .build();
        
        Log.d(TAG, "HTTP client initialized for ESP32 IP: " + esp32IP);
    }
    
    public void setConnectionCallback(ConnectionCallback callback) {
        this.connectionCallback = callback;
    }
    
    public void updateIP(String newIP) {
        this.esp32IP = newIP;
        Log.d(TAG, "ESP32 IP updated to: " + newIP);
    }
    
    /**
     * Test connection to ESP32
     */
    public boolean testConnection() {
        try {
            String url = "http://" + esp32IP + ":" + ESP32_PORT + "/status";
            Request request = new Request.Builder()
                .url(url)
                .get()
                .build();
            
            Log.d(TAG, "Testing connection to: " + url);
            
            try (Response response = httpClient.newCall(request).execute()) {
                boolean success = response.isSuccessful();
                String message = success ? "Connected to ESP32" : "Connection failed";
                
                if (connectionCallback != null) {
                    connectionCallback.onConnectionStatusChanged(success, message);
                }
                
                Log.d(TAG, "Connection test result: " + success);
                return success;
            }
        } catch (IOException e) {
            String errorMessage = "Connection failed: " + e.getMessage();
            Log.e(TAG, errorMessage, e);
            
            if (connectionCallback != null) {
                connectionCallback.onConnectionStatusChanged(false, errorMessage);
            }
            return false;
        }
    }
    
    /**
     * Send command to ESP32
     */
    public boolean sendCommand(String command) {
        try {
            String url = "http://" + esp32IP + ":" + ESP32_PORT + "/cmd?command=" + command;
            Request request = new Request.Builder()
                .url(url)
                .get()
                .build();
            
            Log.d(TAG, "Sending command: " + command + " to " + url);
            
            try (Response response = httpClient.newCall(request).execute()) {
                boolean success = response.isSuccessful();
                
                if (success) {
                    String responseBody = response.body() != null ? response.body().string() : "";
                    Log.d(TAG, "Command successful: " + command + " - Response: " + responseBody);
                    
                    if (connectionCallback != null) {
                        connectionCallback.onConnectionStatusChanged(true, "Command executed: " + command);
                    }
                } else {
                    Log.e(TAG, "Command failed: " + command + " - HTTP " + response.code());
                    
                    if (connectionCallback != null) {
                        connectionCallback.onConnectionStatusChanged(false, "Command failed: " + command);
                    }
                }
                
                return success;
            }
        } catch (IOException e) {
            String errorMessage = "Command failed: " + command + " - " + e.getMessage();
            Log.e(TAG, errorMessage, e);
            
            if (connectionCallback != null) {
                connectionCallback.onConnectionStatusChanged(false, errorMessage);
            }
            return false;
        }
    }
    
    /**
     * Request distance measurement from ultrasonic sensor
     */
    public float requestDistance() throws IOException {
        String url = "http://" + esp32IP + ":" + ESP32_PORT + "/cmd?command=GET_DISTANCE";
        Request request = new Request.Builder()
            .url(url)
            .get()
            .build();
        
        Log.d(TAG, "Requesting distance from: " + url);
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful() && response.body() != null) {
                String responseBody = response.body().string();
                
                // Parse distance from response
                // Expected format: "GET_DISTANCE executed" or distance value
                try {
                    // Try to extract numeric value from response
                    String[] parts = responseBody.split("\\s+");
                    for (String part : parts) {
                        try {
                            float distance = Float.parseFloat(part);
                            if (distance >= 0 && distance <= 400) { // Valid ultrasonic range
                                Log.d(TAG, "Distance received: " + distance + " cm");
                                return distance;
                            }
                        } catch (NumberFormatException ignored) {
                            // Continue searching for numeric value
                        }
                    }
                    
                    // If no valid distance found, make a separate status request
                    return requestDistanceFromStatus();
                    
                } catch (Exception e) {
                    Log.w(TAG, "Failed to parse distance from response: " + responseBody, e);
                    return requestDistanceFromStatus();
                }
            } else {
                throw new IOException("Failed to get distance - HTTP " + response.code());
            }
        }
    }
    
    /**
     * Request distance from status endpoint
     */
    private float requestDistanceFromStatus() throws IOException {
        String url = "http://" + esp32IP + ":" + ESP32_PORT + "/status";
        Request request = new Request.Builder()
            .url(url)
            .get()
            .build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful() && response.body() != null) {
                String responseBody = response.body().string();
                
                // Parse JSON response for distance
                // Expected format: {"status":"online","distance":25.5,...}
                try {
                    int distanceIndex = responseBody.indexOf("\"distance\":");
                    if (distanceIndex != -1) {
                        String distanceStr = responseBody.substring(distanceIndex + 11);
                        int endIndex = distanceStr.indexOf(",");
                        if (endIndex == -1) {
                            endIndex = distanceStr.indexOf("}");
                        }
                        if (endIndex != -1) {
                            distanceStr = distanceStr.substring(0, endIndex);
                            float distance = Float.parseFloat(distanceStr);
                            Log.d(TAG, "Distance from status: " + distance + " cm");
                            return distance;
                        }
                    }
                } catch (Exception e) {
                    Log.w(TAG, "Failed to parse distance from status: " + responseBody, e);
                }
                
                // Default fallback
                Log.w(TAG, "Could not parse distance, returning default value");
                return 999.0f; // Default "no reading" value
            } else {
                throw new IOException("Failed to get status - HTTP " + response.code());
            }
        }
    }
    
    /**
     * Get robot status
     */
    public String getStatus() {
        try {
            String url = "http://" + esp32IP + ":" + ESP32_PORT + "/status";
            Request request = new Request.Builder()
                .url(url)
                .get()
                .build();
            
            try (Response response = httpClient.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    Log.d(TAG, "Status received: " + responseBody);
                    return responseBody;
                } else {
                    return "{\"status\":\"offline\",\"error\":\"HTTP " + response.code() + "\"}";
                }
            }
        } catch (IOException e) {
            Log.e(TAG, "Failed to get status", e);
            return "{\"status\":\"offline\",\"error\":\"" + e.getMessage() + "\"}";
        }
    }
    
    /**
     * Disconnect and cleanup
     */
    public void disconnect() {
        if (httpClient != null) {
            httpClient.dispatcher().executorService().shutdown();
            httpClient.connectionPool().evictAll();
        }
        Log.d(TAG, "ESP32 communication disconnected");
    }
}
