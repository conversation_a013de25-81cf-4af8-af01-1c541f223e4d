/*
 * STEM Simple Robot Controller - ESP32 Firmware
 * Simplified version for two-wheel robot with basic features:
 * - Two-wheel movement (forward, backward, left, right, stop)
 * - Basic servo control (left arm, right arm)
 * - Ultrasonic sensor for distance measurement
 * - WiFi Access Point for smartphone control
 * - USB Serial for tablet control
 * - Smart greeting functionality
 */

#include <WiFi.h>
#include <WebServer.h>
#include <Servo.h>

// ===== CONFIGURATION =====
// WiFi Access Point Settings
const char* AP_SSID = "STEM-Simple-Robot";
const char* AP_PASSWORD = "robot123";
const IPAddress AP_IP(192, 168, 4, 1);
const IPAddress AP_GATEWAY(192, 168, 4, 1);
const IPAddress AP_SUBNET(255, 255, 255, 0);

// Hardware Pin Definitions
// Two-wheel robot motor pins
const int MOTOR_LEFT_PIN1 = 26;   // Left motor direction pin 1
const int MOTOR_LEFT_PIN2 = 27;   // Left motor direction pin 2
const int MOTOR_RIGHT_PIN1 = 32;  // Right motor direction pin 1
const int MOTOR_RIGHT_PIN2 = 33;  // Right motor direction pin 2

// Servo pins
const int SERVO_LEFT_ARM = 18;    // Left arm servo
const int SERVO_RIGHT_ARM = 19;   // Right arm servo

// Ultrasonic sensor pins
const int ULTRASONIC_TRIG_PIN = 12;
const int ULTRASONIC_ECHO_PIN = 13;

// Status LED
const int STATUS_LED = 2;

// ===== GLOBAL VARIABLES =====
WebServer server(80);
Servo leftArmServo;
Servo rightArmServo;

// Servo positions
int leftArmPosition = 180;   // Rest position for left arm
int rightArmPosition = 0;    // Rest position for right arm

// Movement timing
unsigned long movementStartTime = 0;
unsigned long movementDuration = 0;
bool isMoving = false;
String currentMovement = "STOP";

// Smart greeting
bool smartGreetingEnabled = true;
unsigned long lastGreetingTime = 0;
const unsigned long GREETING_COOLDOWN = 10000; // 10 seconds

// Distance monitoring
bool distanceStreamingEnabled = false;
unsigned long lastDistanceStream = 0;
const unsigned long DISTANCE_STREAM_INTERVAL = 500; // 500ms

void setup() {
  // Initialize Serial communication
  Serial.begin(115200);
  Serial.println("\n=== STEM Simple Robot Controller v1.0 ===");
  Serial.println("Two-wheel robot with basic features");
  
  // Initialize hardware pins
  initializeHardware();
  
  // Start WiFi Access Point
  startWiFiAccessPoint();
  
  // Setup web server
  setupWebServer();
  
  // Initialize servos to rest position
  moveToRestPosition();
  
  Serial.println("System ready!");
  Serial.println("Commands: F, B, L, R, S, WAVE, POINT, REST, GET_DISTANCE");
  Serial.println("Smart Greeting: Face detection + distance ≤30cm → handshake");
}

void initializeHardware() {
  // Motor pins
  pinMode(MOTOR_LEFT_PIN1, OUTPUT);
  pinMode(MOTOR_LEFT_PIN2, OUTPUT);
  pinMode(MOTOR_RIGHT_PIN1, OUTPUT);
  pinMode(MOTOR_RIGHT_PIN2, OUTPUT);
  
  // Ultrasonic sensor pins
  pinMode(ULTRASONIC_TRIG_PIN, OUTPUT);
  pinMode(ULTRASONIC_ECHO_PIN, INPUT);
  
  // Status LED
  pinMode(STATUS_LED, OUTPUT);
  
  // Attach servos
  leftArmServo.attach(SERVO_LEFT_ARM);
  rightArmServo.attach(SERVO_RIGHT_ARM);
  
  // Stop all motors initially
  stopRobot();
  
  Serial.println("Hardware initialized");
}

void startWiFiAccessPoint() {
  WiFi.mode(WIFI_AP);
  WiFi.softAPConfig(AP_IP, AP_GATEWAY, AP_SUBNET);
  WiFi.softAP(AP_SSID, AP_PASSWORD);
  
  Serial.println("WiFi Access Point started");
  Serial.print("SSID: ");
  Serial.println(AP_SSID);
  Serial.print("IP: ");
  Serial.println(WiFi.softAPIP());
}

void setupWebServer() {
  // Root page with control interface
  server.on("/", handleRoot);
  
  // Command endpoint
  server.on("/cmd", handleCommand);
  
  // Status endpoint
  server.on("/status", handleStatus);
  
  // Start server
  server.begin();
  Serial.println("Web server started on port 80");
}

void loop() {
  // Handle web server requests
  server.handleClient();
  
  // Handle USB Serial commands
  if (Serial.available()) {
    String command = Serial.readStringUntil('\n');
    command.trim();
    processCommand(command);
  }
  
  // Handle timed movement
  if (isMoving && millis() - movementStartTime >= movementDuration) {
    stopRobot();
    isMoving = false;
    Serial.println("USB_STATUS: Movement completed - STOPPED");
  }
  
  // Handle distance streaming
  if (distanceStreamingEnabled && millis() - lastDistanceStream >= DISTANCE_STREAM_INTERVAL) {
    float distance = getUltrasonicDistance();
    Serial.println("USB_DISTANCE: " + String(distance) + " cm");
    lastDistanceStream = millis();
  }
  
  // Update status LED
  digitalWrite(STATUS_LED, WiFi.softAPgetStationNum() > 0 ? HIGH : LOW);
  
  delay(1); // Minimal delay for fastest response
}

void processCommand(String cmd) {
  cmd.toUpperCase();
  Serial.println("USB_CMD: " + cmd + " - Processing...");
  
  // Movement commands
  if (cmd == "F") {
    goForward();
    Serial.println("USB_STATUS: Moving Forward - ACTIVE");
  }
  else if (cmd == "B") {
    goBackward();
    Serial.println("USB_STATUS: Moving Backward - ACTIVE");
  }
  else if (cmd == "L") {
    turnLeft();
    Serial.println("USB_STATUS: Turning Left - ACTIVE");
  }
  else if (cmd == "R") {
    turnRight();
    Serial.println("USB_STATUS: Turning Right - ACTIVE");
  }
  else if (cmd == "S") {
    stopRobot();
    Serial.println("USB_STATUS: Robot Stopped - INACTIVE");
  }
  // Servo commands
  else if (cmd == "WAVE") {
    performWave();
    Serial.println("USB_STATUS: Wave Gesture - EXECUTING");
  }
  else if (cmd == "POINT") {
    performPoint();
    Serial.println("USB_STATUS: Point Gesture - EXECUTING");
  }
  else if (cmd == "REST") {
    moveToRestPosition();
    Serial.println("USB_STATUS: Rest Position - COMPLETED");
  }
  else if (cmd == "HANDSHAKE") {
    performHandshake();
    Serial.println("USB_STATUS: Handshake Gesture - EXECUTING");
  }
  // Configurable servo commands
  else if (cmd.startsWith("SERVO_RIGHT:")) {
    int angle = cmd.substring(12).toInt();
    if (angle >= 0 && angle <= 180) {
      rightArmServo.write(angle);
      Serial.println("USB_STATUS: Right Servo " + String(angle) + "° - COMPLETED");
    } else {
      Serial.println("USB_ERROR: Invalid servo angle: " + String(angle));
    }
  }
  else if (cmd.startsWith("SERVO_LEFT:")) {
    int angle = cmd.substring(11).toInt();
    if (angle >= 0 && angle <= 180) {
      leftArmServo.write(angle);
      Serial.println("USB_STATUS: Left Servo " + String(angle) + "° - COMPLETED");
    } else {
      Serial.println("USB_ERROR: Invalid servo angle: " + String(angle));
    }
  }
  // Sensor commands
  else if (cmd == "GET_DISTANCE") {
    float distance = getUltrasonicDistance();
    Serial.println("USB_DISTANCE: " + String(distance) + " cm");
  }
  else if (cmd == "STREAM_DISTANCE_ON") {
    distanceStreamingEnabled = true;
    Serial.println("USB_STATUS: Distance Streaming Enabled - ACTIVE");
  }
  else if (cmd == "STREAM_DISTANCE_OFF") {
    distanceStreamingEnabled = false;
    Serial.println("USB_STATUS: Distance Streaming Disabled - INACTIVE");
  }
  else {
    Serial.println("USB_ERROR: Unknown command: " + cmd);
    Serial.println("USB_HELP: Available commands: F,B,L,R,S,WAVE,POINT,REST,HANDSHAKE,GET_DISTANCE");
  }
}

// ===== MOVEMENT FUNCTIONS =====
void goForward() {
  digitalWrite(MOTOR_LEFT_PIN1, HIGH);
  digitalWrite(MOTOR_LEFT_PIN2, LOW);
  digitalWrite(MOTOR_RIGHT_PIN1, HIGH);
  digitalWrite(MOTOR_RIGHT_PIN2, LOW);
  currentMovement = "FORWARD";
}

void goBackward() {
  digitalWrite(MOTOR_LEFT_PIN1, LOW);
  digitalWrite(MOTOR_LEFT_PIN2, HIGH);
  digitalWrite(MOTOR_RIGHT_PIN1, LOW);
  digitalWrite(MOTOR_RIGHT_PIN2, HIGH);
  currentMovement = "BACKWARD";
}

void turnLeft() {
  digitalWrite(MOTOR_LEFT_PIN1, LOW);
  digitalWrite(MOTOR_LEFT_PIN2, HIGH);
  digitalWrite(MOTOR_RIGHT_PIN1, HIGH);
  digitalWrite(MOTOR_RIGHT_PIN2, LOW);
  currentMovement = "LEFT";
}

void turnRight() {
  digitalWrite(MOTOR_LEFT_PIN1, HIGH);
  digitalWrite(MOTOR_LEFT_PIN2, LOW);
  digitalWrite(MOTOR_RIGHT_PIN1, LOW);
  digitalWrite(MOTOR_RIGHT_PIN2, HIGH);
  currentMovement = "RIGHT";
}

void stopRobot() {
  digitalWrite(MOTOR_LEFT_PIN1, LOW);
  digitalWrite(MOTOR_LEFT_PIN2, LOW);
  digitalWrite(MOTOR_RIGHT_PIN1, LOW);
  digitalWrite(MOTOR_RIGHT_PIN2, LOW);
  currentMovement = "STOP";
}

// ===== SERVO FUNCTIONS =====
void moveToRestPosition() {
  leftArmServo.write(leftArmPosition);   // 180 degrees
  rightArmServo.write(rightArmPosition); // 0 degrees
  delay(200); // Reduced delay for faster response
}

void performWave() {
  // Right arm wave - faster movement
  rightArmServo.write(90);
  delay(300);
  rightArmServo.write(45);
  delay(300);
  rightArmServo.write(90);
  delay(300);
  rightArmServo.write(rightArmPosition); // Return to rest
}

void performPoint() {
  // Right arm point forward - faster response
  rightArmServo.write(90);
  delay(800); // Reduced delay
  rightArmServo.write(rightArmPosition); // Return to rest
}

void performHandshake() {
  // Right arm handshake position - configurable duration
  rightArmServo.write(90);
  delay(2000); // Reduced to 2 seconds for faster interaction
  rightArmServo.write(rightArmPosition); // Return to rest
}

// ===== SENSOR FUNCTIONS =====
float getUltrasonicDistance() {
  digitalWrite(ULTRASONIC_TRIG_PIN, LOW);
  delayMicroseconds(2);
  digitalWrite(ULTRASONIC_TRIG_PIN, HIGH);
  delayMicroseconds(10);
  digitalWrite(ULTRASONIC_TRIG_PIN, LOW);
  
  long duration = pulseIn(ULTRASONIC_ECHO_PIN, HIGH, 30000); // 30ms timeout
  if (duration == 0) {
    return 999.0; // No echo received
  }
  
  float distance = (duration * 0.034) / 2; // Convert to cm
  return distance;
}

// ===== WEB SERVER HANDLERS =====
void handleRoot() {
  String html = R"rawliteral(
<!DOCTYPE html>
<html>
<head>
    <title>STEM Simple Robot</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body { font-family: Arial; text-align: center; margin: 20px; }
        .button { background-color: #4CAF50; border: none; color: white; 
                 padding: 15px 32px; text-align: center; font-size: 16px; 
                 margin: 4px 2px; cursor: pointer; border-radius: 8px; }
        .stop { background-color: #f44336; }
        .servo { background-color: #2196F3; }
        .status { margin: 20px; padding: 10px; background-color: #f0f0f0; }
    </style>
</head>
<body>
    <h1>🤖 STEM Simple Robot</h1>
    <div class="status" id="status">Status: Ready</div>
    
    <h3>Movement Controls</h3>
    <button class="button" onclick="sendCommand('F')">Forward</button><br>
    <button class="button" onclick="sendCommand('L')">Left</button>
    <button class="button stop" onclick="sendCommand('S')">STOP</button>
    <button class="button" onclick="sendCommand('R')">Right</button><br>
    <button class="button" onclick="sendCommand('B')">Backward</button><br>
    
    <h3>Servo Controls</h3>
    <button class="button servo" onclick="sendCommand('WAVE')">Wave</button>
    <button class="button servo" onclick="sendCommand('POINT')">Point</button>
    <button class="button servo" onclick="sendCommand('REST')">Rest</button>
    <button class="button servo" onclick="sendCommand('HANDSHAKE')">Handshake</button><br>
    
    <h3>Sensors</h3>
    <button class="button" onclick="getDistance()">Check Distance</button>
    <div id="distance">Distance: -- cm</div>
    
    <script>
        function sendCommand(cmd) {
            fetch('/cmd?command=' + cmd)
                .then(response => response.text())
                .then(data => {
                    document.getElementById('status').innerHTML = 'Status: ' + data;
                });
        }
        
        function getDistance() {
            fetch('/cmd?command=GET_DISTANCE')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('distance').innerHTML = 'Distance: ' + data;
                });
        }
    </script>
</body>
</html>
)rawliteral";
  
  server.send(200, "text/html", html);
}

void handleCommand() {
  if (server.hasArg("command")) {
    String command = server.arg("command");
    processCommand(command);
    server.send(200, "text/plain", command + " executed");
  } else {
    server.send(400, "text/plain", "No command specified");
  }
}

void handleStatus() {
  String json = "{";
  json += "\"status\":\"online\",";
  json += "\"movement\":\"" + currentMovement + "\",";
  json += "\"distance\":" + String(getUltrasonicDistance()) + ",";
  json += "\"left_arm\":" + String(leftArmPosition) + ",";
  json += "\"right_arm\":" + String(rightArmPosition);
  json += "}";
  
  server.send(200, "application/json", json);
}
