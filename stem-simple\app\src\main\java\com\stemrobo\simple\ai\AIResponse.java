package com.stemrobo.simple.ai;

/**
 * AI Response structure for STEM Simple Robot
 * Contains both natural language response and executable commands
 */
public class AIResponse {
    private String response;
    private AICommand[] commands;
    private String presetName;
    
    public AIResponse(String response, AICommand[] commands, String presetName) {
        this.response = response;
        this.commands = commands;
        this.presetName = presetName;
    }
    
    // Getters
    public String getResponse() {
        return response;
    }
    
    public AICommand[] getCommands() {
        return commands;
    }
    
    public String getPresetName() {
        return presetName;
    }
    
    // Setters
    public void setResponse(String response) {
        this.response = response;
    }
    
    public void setCommands(AICommand[] commands) {
        this.commands = commands;
    }
    
    public void setPresetName(String presetName) {
        this.presetName = presetName;
    }
    
    // Utility methods
    public boolean hasCommands() {
        return commands != null && commands.length > 0;
    }
    
    public boolean isPresetCreation() {
        return presetName != null && !presetName.trim().isEmpty();
    }
    
    public int getCommandCount() {
        return commands != null ? commands.length : 0;
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("AIResponse{");
        sb.append("response='").append(response).append('\'');
        sb.append(", commandCount=").append(getCommandCount());
        if (presetName != null) {
            sb.append(", presetName='").append(presetName).append('\'');
        }
        sb.append('}');
        return sb.toString();
    }
}
