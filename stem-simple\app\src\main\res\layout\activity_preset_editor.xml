<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary">

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="80dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Preset Info Section -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Preset Information"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="16dp" />

                    <!-- Preset Name -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:hint="Preset Name">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edit_preset_name"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text"
                            android:maxLines="1" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Preset Description -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Description (optional)">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edit_preset_description"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textMultiLine"
                            android:maxLines="3" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Commands Section -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <!-- Commands Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="16dp"
                        android:gravity="center_vertical">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Timeline Steps"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_primary" />

                        <TextView
                            android:id="@+id/command_count"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0 steps"
                            android:textSize="14sp"
                            android:textColor="@color/text_secondary"
                            android:layout_marginEnd="8dp" />

                        <TextView
                            android:id="@+id/text_total_duration"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0s"
                            android:textSize="12sp"
                            android:textColor="@color/text_secondary"
                            android:background="@drawable/rounded_background"
                            android:padding="6dp"
                            android:layout_marginEnd="8dp" />

                        <Button
                            android:id="@+id/btn_add_step"
                            android:layout_width="wrap_content"
                            android:layout_height="32dp"
                            android:text="+ Step"
                            android:textSize="12sp"
                            android:background="@color/button_success"
                            android:textColor="@android:color/white"
                            android:minWidth="0dp"
                            android:paddingStart="12dp"
                            android:paddingEnd="12dp" />

                    </LinearLayout>

                    <!-- Commands List -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/commands_recycler_view"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false"
                        android:paddingStart="16dp"
                        android:paddingEnd="16dp"
                        android:paddingBottom="16dp" />

                    <!-- Empty State -->
                    <LinearLayout
                        android:id="@+id/empty_commands_state"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="32dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="⚙️"
                            android:textSize="48sp"
                            android:layout_marginBottom="16dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="No timeline steps yet"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_primary"
                            android:layout_marginBottom="8dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Create timeline steps to organize your preset actions"
                            android:textSize="14sp"
                            android:textColor="@color/text_secondary"
                            android:gravity="center" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Command Editor Section (Initially Hidden) -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/command_editor_section"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:visibility="gone"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Add Command"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="16dp" />

                    <!-- Command Type -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="12dp">

                        <TextView
                            android:layout_width="80dp"
                            android:layout_height="wrap_content"
                            android:text="Type:"
                            android:textSize="14sp"
                            android:textColor="@color/text_primary"
                            android:gravity="center_vertical" />

                        <Spinner
                            android:id="@+id/spinner_command_type"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1" />

                    </LinearLayout>

                    <!-- Command Action -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="12dp">

                        <TextView
                            android:layout_width="80dp"
                            android:layout_height="wrap_content"
                            android:text="Action:"
                            android:textSize="14sp"
                            android:textColor="@color/text_primary"
                            android:gravity="center_vertical" />

                        <Spinner
                            android:id="@+id/spinner_command_action"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1" />

                    </LinearLayout>

                    <!-- Duration -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="12dp">

                        <TextView
                            android:layout_width="80dp"
                            android:layout_height="wrap_content"
                            android:text="Duration:"
                            android:textSize="14sp"
                            android:textColor="@color/text_primary"
                            android:gravity="center_vertical" />

                        <EditText
                            android:id="@+id/edit_command_duration"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:hint="Duration (ms)"
                            android:inputType="number"
                            android:background="@drawable/edit_text_background"
                            android:padding="8dp" />

                    </LinearLayout>

                    <!-- Parameter -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="16dp">

                        <TextView
                            android:layout_width="80dp"
                            android:layout_height="wrap_content"
                            android:text="Parameter:"
                            android:textSize="14sp"
                            android:textColor="@color/text_primary"
                            android:gravity="center_vertical" />

                        <EditText
                            android:id="@+id/edit_command_parameter"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:hint="Optional parameter"
                            android:inputType="text"
                            android:background="@drawable/edit_text_background"
                            android:padding="8dp" />

                    </LinearLayout>

                    <!-- Add Command Button -->
                    <Button
                        android:id="@+id/btn_add_command_to_list"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Add Command"
                        android:background="@color/button_success"
                        android:textColor="@android:color/white" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Bottom Action Bar -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:orientation="horizontal"
        android:background="@color/surface_light"
        android:padding="16dp"
        android:elevation="8dp">

        <Button
            android:id="@+id/btn_cancel_preset"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:text="Cancel"
            android:background="@color/button_secondary"
            android:textColor="@android:color/white" />

        <Button
            android:id="@+id/btn_test_preset"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="4dp"
            android:layout_marginEnd="4dp"
            android:text="Test"
            android:background="@color/button_primary"
            android:textColor="@android:color/white" />

        <Button
            android:id="@+id/btn_save_preset"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:text="Save"
            android:background="@color/button_success"
            android:textColor="@android:color/white" />

    </LinearLayout>

    <!-- Floating Action Button -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_add_command"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:layout_marginBottom="96dp"
        android:src="@drawable/ic_add"
        android:contentDescription="Add command"
        app:tint="@android:color/white"
        app:backgroundTint="@color/primary_blue" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
