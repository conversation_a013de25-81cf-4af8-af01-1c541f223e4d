<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@color/background_primary">

    <!-- Header -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/preset_management"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="@color/text_primary"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <!-- Preset Creation Section -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/create_preset"
                android:textSize="18sp"
                android:textStyle="bold"
                android:layout_marginBottom="16dp" />

            <!-- Preset Name Input -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:hint="@string/preset_name">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edit_preset_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Preset Description Input -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="@string/preset_description">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edit_preset_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textMultiLine"
                    android:maxLines="3" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Action Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="end">

                <Button
                    android:id="@+id/btn_start_recording"
                    style="@style/ButtonPrimary"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/start_recording"
                    android:layout_marginEnd="8dp" />

                <Button
                    android:id="@+id/btn_stop_recording"
                    style="@style/ButtonSecondary"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/stop_recording"
                    android:layout_marginEnd="8dp"
                    android:enabled="false" />

                <Button
                    android:id="@+id/btn_save_preset"
                    style="@style/ButtonSuccess"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/save_preset"
                    android:enabled="false" />

            </LinearLayout>

            <!-- Recording Status -->
            <TextView
                android:id="@+id/text_recording_status"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/ready_to_record"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:gravity="center"
                android:layout_marginTop="8dp" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Preset List Section -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Header with controls -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="16dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/saved_presets"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <Button
                    android:id="@+id/btn_refresh_presets"
                    style="@style/ButtonSecondary"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/refresh"
                    android:layout_marginEnd="8dp" />

                <Button
                    android:id="@+id/btn_import_preset"
                    style="@style/ButtonSecondary"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/import_preset" />

            </LinearLayout>

            <!-- Preset List -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_presets"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:scrollbars="vertical" />

            <!-- Empty State -->
            <LinearLayout
                android:id="@+id/layout_empty_state"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/no_presets_found"
                    android:textSize="16sp"
                    android:textColor="@color/text_secondary"
                    android:layout_marginBottom="16dp" />

                <Button
                    android:id="@+id/btn_create_first_preset"
                    style="@style/ButtonPrimary"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/create_first_preset" />

            </LinearLayout>

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Quick Actions -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:layout_marginTop="16dp">

        <Button
            android:id="@+id/btn_export_all"
            style="@style/ButtonSecondary"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/export_all"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/btn_delete_all"
            style="@style/ButtonDanger"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/delete_all"
            android:layout_marginStart="8dp" />

    </LinearLayout>

</LinearLayout>
