<resources>
    <string name="app_name">STEM Simple Robot</string>
    
    <!-- Tab titles -->
    <string name="tab_control">Control</string>
    <string name="tab_ai">AI Assistant</string>
    <string name="tab_presets">Presets</string>
    <string name="tab_settings">Settings</string>
    
    <!-- Control tab -->
    <string name="movement_controls">Movement Controls</string>
    <string name="servo_controls">Servo Controls</string>
    <string name="sensor_status">Sensor Status</string>
    <string name="forward">Forward</string>
    <string name="backward">Backward</string>
    <string name="left">Left</string>
    <string name="right">Right</string>
    <string name="stop">Stop</string>
    <string name="wave">Wave</string>
    <string name="point">Point</string>
    <string name="rest">Rest</string>
    <string name="handshake">Handshake</string>
    <string name="arm_controls">Arm Controls</string>
    <string name="head_controls">Head Controls</string>
    <string name="left_arm">Left Arm</string>
    <string name="right_arm">Right Arm</string>
    <string name="head_position">Head</string>
    <string name="gestures">Gestures</string>
    <string name="look_left">Look Left</string>
    <string name="look_center">Look Center</string>
    <string name="look_right">Look Right</string>
    <string name="voice_settings">Voice Settings</string>
    <string name="auto_voice_recognition">Auto Voice Recognition</string>

    <!-- Preset Management -->
    <string name="preset_management">Preset Management</string>
    <string name="create_preset">Create New Preset</string>
    <string name="preset_name">Preset Name</string>
    <string name="preset_description">Description (Optional)</string>
    <string name="start_recording">Start Recording</string>
    <string name="stop_recording">Stop Recording</string>
    <string name="save_preset">Save Preset</string>
    <string name="ready_to_record">Ready to record robot actions</string>
    <string name="recording_actions">Recording... Perform robot actions</string>
    <string name="saved_presets">Saved Presets</string>
    <string name="refresh">Refresh</string>
    <string name="import_preset">Import</string>
    <string name="export_all">Export All</string>
    <string name="delete_all">Delete All</string>
    <string name="no_presets_found">No presets found</string>
    <string name="create_first_preset">Create Your First Preset</string>
    <string name="execute">Execute</string>
    <string name="edit">Edit</string>
    <string name="export">Export</string>
    <string name="delete">Delete</string>
    <string name="action_count">%1$d actions</string>

    <!-- AI Settings -->
    <string name="ai_context_memory">Context Memory Size</string>
    <string name="ai_personality">AI Personality</string>
    <string name="ai_response_style">Response Style</string>
    <string name="ai_verbose_mode">Verbose Mode</string>
    <string name="clear_ai_history">Clear AI History</string>

    <!-- AI Personality Options -->
    <string name="personality_helpful_assistant">Helpful Assistant</string>
    <string name="personality_friendly_companion">Friendly Companion</string>
    <string name="personality_professional_tutor">Professional Tutor</string>
    <string name="personality_playful_friend">Playful Friend</string>

    <!-- AI Response Style Options -->
    <string name="style_conversational">Conversational</string>
    <string name="style_formal">Formal</string>
    <string name="style_casual">Casual</string>
    <string name="style_educational">Educational</string>
    <string name="manage_presets">Manage Presets</string>

    <!-- Movement Duration Settings -->
    <string name="movement_duration_settings">Movement Duration Settings</string>
    <string name="forward_duration">Forward Duration</string>
    <string name="backward_duration">Backward Duration</string>
    <string name="left_duration">Left Turn Duration</string>
    <string name="right_duration">Right Turn Duration</string>

    <!-- Servo Angle Settings -->
    <string name="servo_angle_settings">Servo Angle Settings</string>
    <string name="handshake_angle">Handshake Angle</string>
    <string name="wave_angle">Wave Angle</string>
    <string name="point_angle">Point Angle</string>
    <string name="distance">Distance: %1$s cm</string>
    <string name="face_count">Faces: %1$d</string>
    
    <!-- AI tab -->
    <string name="ai_assistant">AI Assistant</string>
    <string name="speak_command">Speak your command</string>
    <string name="type_command">Type your command</string>
    <string name="send">Send</string>
    <string name="listening">Listening...</string>
    <string name="processing">Processing...</string>
    <string name="preset_created">Preset created: %1$s</string>
    <string name="preset_executed">Executing preset: %1$s</string>
    
    <!-- Settings tab -->
    <string name="robot_settings">Robot Settings</string>
    <string name="ai_settings">AI Settings</string>
    <string name="greeting_settings">Smart Greeting Settings</string>
    <string name="esp32_ip">ESP32 IP Address</string>
    <string name="greeting_enabled">Enable Smart Greeting</string>
    <string name="greeting_distance">Greeting Distance (cm)</string>
    <string name="greeting_cooldown">Greeting Cooldown (seconds)</string>
    <string name="ai_wake_word">AI Wake Word</string>
    <string name="language">Language</string>
    <string name="voice_gender">Voice Gender</string>
    <string name="male">Male</string>
    <string name="female">Female</string>
    
    <!-- Status messages -->
    <string name="robot_online">Robot Online</string>
    <string name="robot_offline">Robot Offline</string>
    <string name="connecting">Connecting...</string>
    <string name="connection_failed">Connection Failed</string>
    <string name="camera_permission_required">Camera permission required</string>
    <string name="microphone_permission_required">Microphone permission required</string>
    
    <!-- Error messages -->
    <string name="error_camera_init">Failed to initialize camera</string>
    <string name="error_ai_service">AI service unavailable</string>
    <string name="error_robot_communication">Robot communication error</string>
    <string name="error_invalid_command">Invalid command</string>
</resources>
