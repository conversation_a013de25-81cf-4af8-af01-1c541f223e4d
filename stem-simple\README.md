# STEM Simple Robot - Android Application

A simplified and powerful Android application for controlling a two-wheel STEM robot with advanced AI capabilities, face detection, and smart greeting features.

## 🚀 Features

### Core Robot Control
- **Two-wheel movement**: Forward, backward, left, right, stop
- **Servo control**: Wave, point, rest, handshake gestures
- **Real-time sensor monitoring**: Ultrasonic distance measurement
- **WiFi and USB communication** with ESP32 controller

### Smart Vision System
- **Real-time face detection** using ML Kit
- **Face count display** in status bar
- **Smart greeting system** with face + distance detection
- **Configurable greeting settings** (distance threshold, cooldown)

### Unified AI Assistant
- **Structured AI responses** with executable commands
- **Voice recognition** with wake word detection
- **Text-to-speech** with gender selection
- **Preset creation and execution** through natural language
- **Command parsing and execution** system

### User Interface
- **Clean 3-tab design**: Control, AI Assistant, Settings
- **Real-time status monitoring** in top bar
- **Mini camera preview** for face detection
- **Comprehensive settings** for all features

## 📱 Application Structure

```
stem-simple/
├── app/src/main/java/com/stemrobo/simple/
│   ├── MainActivity.java              # Main activity with tab navigation
│   ├── ai/                           # AI system components
│   │   ├── UnifiedAIService.java     # AI service with structured responses
│   │   ├── CommandParser.java        # Command execution engine
│   │   ├── PresetManager.java        # Preset creation and management
│   │   ├── AIResponse.java           # AI response structure
│   │   ├── AICommand.java            # Command structure
│   │   └── RobotPreset.java          # Preset data structure
│   ├── robot/                        # Robot control
│   │   ├── SimpleRobotController.java # Main robot controller
│   │   └── ESP32SimpleComm.java      # ESP32 communication
│   ├── vision/                       # Vision and greeting
│   │   ├── SimpleFaceDetection.java  # Face detection system
│   │   └── SmartGreeting.java        # Smart greeting logic
│   ├── services/                     # Background services
│   │   ├── VoiceService.java         # Voice recognition
│   │   └── TTSService.java           # Text-to-speech
│   └── ui/                          # User interface
│       ├── ControlFragment.java      # Robot control tab
│       ├── AIFragment.java           # AI assistant tab
│       ├── SettingsFragment.java     # Settings tab
│       └── [Adapters and UI classes]
└── esp32-simple-controller.ino       # ESP32 firmware
```

## 🔧 Hardware Requirements

### ESP32 Controller
- ESP32 development board
- WiFi capability for wireless control
- USB connection for direct communication

### Robot Hardware
- **Two-wheel robot chassis**
- **Motor driver** (compatible with ESP32)
- **Servo motors** for arm movements (2x)
- **Ultrasonic sensor** (HC-SR04 or similar)
- **Power supply** for motors and ESP32

### Pin Configuration (ESP32)
```cpp
// Motor pins (two-wheel robot)
MOTOR_LEFT_PIN1 = 26    // Left motor direction 1
MOTOR_LEFT_PIN2 = 27    // Left motor direction 2
MOTOR_RIGHT_PIN1 = 32   // Right motor direction 1
MOTOR_RIGHT_PIN2 = 33   // Right motor direction 2

// Servo pins
SERVO_LEFT_ARM = 18     // Left arm servo
SERVO_RIGHT_ARM = 19    // Right arm servo

// Ultrasonic sensor
ULTRASONIC_TRIG_PIN = 12
ULTRASONIC_ECHO_PIN = 13

// Status LED
STATUS_LED = 2
```

## 📋 Setup Instructions

### 1. ESP32 Setup
1. Upload `esp32-simple-controller.ino` to your ESP32
2. Connect hardware according to pin configuration
3. Power on the ESP32 - it will create WiFi AP "STEM-Simple-Robot"

### 2. Android App Setup
1. Install the APK on your Android device
2. Grant required permissions:
   - Camera (for face detection)
   - Microphone (for voice commands)
   - Location (for GPS features)
3. Connect to ESP32 WiFi or use USB connection

### 3. Configuration
1. Open **Settings** tab
2. Set ESP32 IP address (default: ***********)
3. Test connection
4. Configure smart greeting settings
5. Set AI wake word and voice preferences

## 🎮 Usage Guide

### Basic Robot Control
1. Go to **Control** tab
2. Use directional buttons for movement (hold to move)
3. Use servo buttons for gestures
4. Monitor distance and face count in real-time

### AI Assistant
1. Go to **AI** tab
2. **Voice commands**: Press voice button and say wake word + command
3. **Text commands**: Type commands and press send
4. **Presets**: Execute saved command sequences
5. **Create presets**: Say "create preset [name]" with commands

### Smart Greeting
1. Enable in **Settings** tab
2. Configure distance threshold (default: 30cm)
3. Set cooldown period (default: 10 seconds)
4. Robot will automatically greet when face detected + distance condition met

## 🗣️ Voice Commands Examples

```
"Hey robot, move forward for 3 seconds"
"Hey robot, wave and say hello"
"Hey robot, create preset greeting with wave and handshake"
"Hey robot, execute greeting preset"
"Hey robot, check distance"
"Hey robot, stop all movement"
```

## 🔧 AI Response Format

The AI system uses structured JSON responses:

```json
{
  "commands": [
    {"type": "movement", "action": "forward", "duration": 3000},
    {"type": "servo", "action": "wave"},
    {"type": "sensor", "action": "get_distance"}
  ],
  "response": "Moving forward and waving!",
  "preset_name": "greeting_sequence"
}
```

## 📊 Performance Features

- **Optimized face detection** with 500ms intervals
- **Efficient ESP32 communication** with retry logic
- **Background distance monitoring** every 1 second
- **Smooth servo movements** with configurable delays
- **Memory-efficient preset storage** using SharedPreferences

## 🛠️ Troubleshooting

### Connection Issues
- Check ESP32 WiFi AP is active
- Verify IP address in settings
- Try USB connection as fallback
- Check firewall settings

### Face Detection Issues
- Ensure camera permission granted
- Check lighting conditions
- Verify camera is not blocked
- Restart app if camera fails

### Voice Recognition Issues
- Check microphone permission
- Ensure quiet environment
- Verify wake word pronunciation
- Check device language settings

## 🔄 Updates and Improvements

### Completed Features
- ✅ Simplified two-wheel robot control
- ✅ Real-time face detection and counting
- ✅ Smart greeting with distance validation
- ✅ Unified AI system with structured responses
- ✅ Voice recognition with wake word
- ✅ Preset creation and execution
- ✅ Comprehensive settings interface
- ✅ Clean 3-tab UI design

### Future Enhancements
- 🔄 Advanced AI conversation memory
- 🔄 Multiple robot support
- 🔄 Cloud-based AI processing
- 🔄 Advanced gesture recognition
- 🔄 Mobile robot mapping

## 📄 License

This project is part of the STEM robotics educational platform.

## 🤝 Contributing

This is a simplified version designed for educational purposes. 
For contributions or issues, please refer to the main STEM robotics project.

---

**STEM Simple Robot** - Making robotics accessible and powerful! 🤖✨
