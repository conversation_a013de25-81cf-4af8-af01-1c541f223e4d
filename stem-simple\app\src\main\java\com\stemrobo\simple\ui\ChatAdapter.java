package com.stemrobo.simple.ui;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.stemrobo.simple.R;
import java.util.List;

/**
 * Chat Adapter for AI chat interface
 * Displays different message types with appropriate styling
 */
public class ChatAdapter extends RecyclerView.Adapter<ChatAdapter.ChatViewHolder> {
    
    private static final int VIEW_TYPE_USER = 1;
    private static final int VIEW_TYPE_AI = 2;
    private static final int VIEW_TYPE_SYSTEM = 3;
    
    private final List<ChatMessage> messages;
    
    public ChatAdapter(List<ChatMessage> messages) {
        this.messages = messages;
    }
    
    @Override
    public int getItemViewType(int position) {
        ChatMessage message = messages.get(position);
        switch (message.getType()) {
            case USER:
                return VIEW_TYPE_USER;
            case AI:
                return VIEW_TYPE_AI;
            case SYSTEM:
                return VIEW_TYPE_SYSTEM;
            default:
                return VIEW_TYPE_SYSTEM;
        }
    }
    
    @NonNull
    @Override
    public ChatViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        LayoutInflater inflater = LayoutInflater.from(parent.getContext());
        View view;
        
        switch (viewType) {
            case VIEW_TYPE_USER:
                view = inflater.inflate(R.layout.item_chat_user, parent, false);
                break;
            case VIEW_TYPE_AI:
                view = inflater.inflate(R.layout.item_chat_robot, parent, false);
                break;
            case VIEW_TYPE_SYSTEM:
                view = inflater.inflate(R.layout.item_chat_system, parent, false);
                break;
            default:
                view = inflater.inflate(R.layout.item_chat_system, parent, false);
                break;
        }
        
        return new ChatViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull ChatViewHolder holder, int position) {
        ChatMessage message = messages.get(position);
        holder.bind(message);
    }
    
    @Override
    public int getItemCount() {
        return messages.size();
    }
    
    static class ChatViewHolder extends RecyclerView.ViewHolder {
        private final TextView messageText;
        private final TextView timeText;
        
        public ChatViewHolder(@NonNull View itemView) {
            super(itemView);
            messageText = itemView.findViewById(R.id.message_text);
            timeText = itemView.findViewById(R.id.time_text);
        }
        
        public void bind(ChatMessage message) {
            messageText.setText(message.getMessage());
            if (timeText != null) {
                timeText.setText(message.getFormattedTime());
            }
        }
    }
}
