<resources>
    <string name="app_name">STEM 简单机器人</string>
    
    <!-- Tab titles -->
    <string name="tab_control">控制</string>
    <string name="tab_ai">AI助手</string>
    <string name="tab_settings">设置</string>
    
    <!-- Control tab -->
    <string name="movement_controls">运动控制</string>
    <string name="servo_controls">舵机控制</string>
    <string name="sensor_status">传感器状态</string>
    <string name="forward">前进</string>
    <string name="backward">后退</string>
    <string name="left">左转</string>
    <string name="right">右转</string>
    <string name="stop">停止</string>
    <string name="wave">挥手</string>
    <string name="point">指向</string>
    <string name="rest">休息</string>
    <string name="distance">距离：%1$s 厘米</string>
    <string name="face_count">面孔：%1$d</string>
    
    <!-- AI tab -->
    <string name="ai_assistant">AI助手</string>
    <string name="speak_command">说出您的命令</string>
    <string name="type_command">输入您的命令</string>
    <string name="send">发送</string>
    <string name="listening">正在听...</string>
    <string name="processing">处理中...</string>
    <string name="preset_created">预设已创建：%1$s</string>
    <string name="preset_executed">正在执行预设：%1$s</string>
    
    <!-- Settings tab -->
    <string name="robot_settings">机器人设置</string>
    <string name="ai_settings">AI设置</string>
    <string name="greeting_settings">智能问候设置</string>
    <string name="esp32_ip">ESP32 IP地址</string>
    <string name="greeting_enabled">启用智能问候</string>
    <string name="greeting_distance">问候距离（厘米）</string>
    <string name="greeting_cooldown">问候冷却时间（秒）</string>
    <string name="ai_wake_word">AI唤醒词</string>
    <string name="language">语言</string>
    <string name="voice_gender">语音性别</string>
    <string name="male">男性</string>
    <string name="female">女性</string>
    
    <!-- Status messages -->
    <string name="robot_online">机器人在线</string>
    <string name="robot_offline">机器人离线</string>
    <string name="connecting">连接中...</string>
    <string name="connection_failed">连接失败</string>
    <string name="camera_permission_required">需要相机权限</string>
    <string name="microphone_permission_required">需要麦克风权限</string>
    
    <!-- Error messages -->
    <string name="error_camera_init">相机初始化失败</string>
    <string name="error_ai_service">AI服务不可用</string>
    <string name="error_robot_communication">机器人通信错误</string>
    <string name="error_invalid_command">无效命令</string>
</resources>
