package com.stemrobo.simple.ai;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

/**
 * Preset Manager for STEM Simple Robot
 * Handles creation, storage, and execution of command presets
 */
public class PresetManager {
    private static final String TAG = "PresetManager";
    private static final String PREFS_NAME = "robot_presets";
    private static final String KEY_PRESETS = "presets";
    
    private final Context context;
    private final SharedPreferences preferences;
    private final Gson gson;
    private final CommandParser commandParser;
    private List<RobotPreset> presets;
    
    public interface PresetCallback {
        void onPresetCreated(RobotPreset preset);
        void onPresetExecuted(RobotPreset preset);
        void onPresetError(String error);
        void onPresetsLoaded(List<RobotPreset> presets);
    }
    
    private PresetCallback presetCallback;
    
    public PresetManager(Context context, CommandParser commandParser) {
        this.context = context;
        this.commandParser = commandParser;
        this.preferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        this.gson = new Gson();
        this.presets = new ArrayList<>();
        
        loadPresets();
        Log.d(TAG, "Preset manager initialized with " + presets.size() + " presets");
    }
    
    public void setPresetCallback(PresetCallback callback) {
        this.presetCallback = callback;
    }
    
    /**
     * Create a new preset from AI response
     */
    public void createPreset(String name, AICommand[] commands, String description) {
        if (name == null || name.trim().isEmpty()) {
            Log.w(TAG, "Cannot create preset with empty name");
            if (presetCallback != null) {
                presetCallback.onPresetError("Preset name cannot be empty");
            }
            return;
        }
        
        if (commands == null || commands.length == 0) {
            Log.w(TAG, "Cannot create preset with no commands");
            if (presetCallback != null) {
                presetCallback.onPresetError("Preset must have at least one command");
            }
            return;
        }
        
        // Check if preset with same name already exists
        if (getPresetByName(name) != null) {
            Log.w(TAG, "Preset with name '" + name + "' already exists");
            if (presetCallback != null) {
                presetCallback.onPresetError("Preset with name '" + name + "' already exists");
            }
            return;
        }
        
        // Create new preset
        RobotPreset preset = new RobotPreset(
            generatePresetId(),
            name,
            description != null ? description : "Custom preset created by AI",
            commands,
            System.currentTimeMillis()
        );
        
        // Add to list and save
        presets.add(preset);
        savePresets();
        
        Log.d(TAG, "Created preset: " + name + " with " + commands.length + " commands");
        
        if (presetCallback != null) {
            presetCallback.onPresetCreated(preset);
        }
    }
    
    /**
     * Execute a preset by name
     */
    public void executePreset(String name) {
        RobotPreset preset = getPresetByName(name);
        if (preset == null) {
            Log.w(TAG, "Preset not found: " + name);
            if (presetCallback != null) {
                presetCallback.onPresetError("Preset not found: " + name);
            }
            return;
        }
        
        executePreset(preset);
    }
    
    /**
     * Execute a preset
     */
    public void executePreset(RobotPreset preset) {
        if (preset == null) {
            Log.w(TAG, "Cannot execute null preset");
            if (presetCallback != null) {
                presetCallback.onPresetError("Invalid preset");
            }
            return;
        }
        
        if (commandParser == null) {
            Log.e(TAG, "Command parser not available");
            if (presetCallback != null) {
                presetCallback.onPresetError("Command parser not available");
            }
            return;
        }
        
        Log.d(TAG, "Executing preset: " + preset.getName());
        
        // Update last executed time
        preset.setLastExecuted(System.currentTimeMillis());
        savePresets();
        
        // Execute commands
        commandParser.executeCommands(preset.getCommands());
        
        if (presetCallback != null) {
            presetCallback.onPresetExecuted(preset);
        }
    }
    
    /**
     * Delete a preset
     */
    public void deletePreset(String name) {
        RobotPreset preset = getPresetByName(name);
        if (preset != null) {
            presets.remove(preset);
            savePresets();
            Log.d(TAG, "Deleted preset: " + name);
        } else {
            Log.w(TAG, "Preset not found for deletion: " + name);
        }
    }
    
    /**
     * Get preset by name
     */
    public RobotPreset getPresetByName(String name) {
        for (RobotPreset preset : presets) {
            if (preset.getName().equalsIgnoreCase(name)) {
                return preset;
            }
        }
        return null;
    }

    /**
     * Get preset by ID
     */
    public RobotPreset getPresetById(String id) {
        for (RobotPreset preset : presets) {
            if (preset.getId().equals(id)) {
                return preset;
            }
        }
        return null;
    }

    /**
     * Update an existing preset
     */
    public void updatePreset(String id, String name, String description, AICommand[] commands) {
        RobotPreset preset = getPresetById(id);
        if (preset != null) {
            preset.setName(name);
            preset.setDescription(description);
            preset.setCommands(commands);
            savePresets();
            Log.d(TAG, "Updated preset: " + name);

            if (presetCallback != null) {
                presetCallback.onPresetCreated(preset); // Reuse the created callback for updates
            }
        } else {
            Log.w(TAG, "Preset not found for update: " + id);
            if (presetCallback != null) {
                presetCallback.onPresetError("Preset not found for update");
            }
        }
    }
    
    /**
     * Get all presets
     */
    public List<RobotPreset> getAllPresets() {
        return new ArrayList<>(presets);
    }
    
    /**
     * Get preset names
     */
    public List<String> getPresetNames() {
        List<String> names = new ArrayList<>();
        for (RobotPreset preset : presets) {
            names.add(preset.getName());
        }
        return names;
    }
    
    /**
     * Check if preset exists
     */
    public boolean presetExists(String name) {
        return getPresetByName(name) != null;
    }
    
    /**
     * Get preset count
     */
    public int getPresetCount() {
        return presets.size();
    }
    
    private void loadPresets() {
        try {
            String presetsJson = preferences.getString(KEY_PRESETS, "[]");
            Type listType = new TypeToken<List<RobotPreset>>(){}.getType();
            presets = gson.fromJson(presetsJson, listType);
            
            if (presets == null) {
                presets = new ArrayList<>();
            }
            
            Log.d(TAG, "Loaded " + presets.size() + " presets");
            
            if (presetCallback != null) {
                presetCallback.onPresetsLoaded(presets);
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error loading presets", e);
            presets = new ArrayList<>();
            
            // Create some default presets
            createDefaultPresets();
        }
    }
    
    private void savePresets() {
        try {
            String presetsJson = gson.toJson(presets);
            preferences.edit().putString(KEY_PRESETS, presetsJson).apply();
            Log.d(TAG, "Saved " + presets.size() + " presets");
        } catch (Exception e) {
            Log.e(TAG, "Error saving presets", e);
        }
    }
    
    private void createDefaultPresets() {
        Log.d(TAG, "Creating default presets");
        
        // Greeting sequence
        AICommand[] greetingCommands = {
            new AICommand("servo", "wave", 2000),
            new AICommand("greeting", "trigger_greeting", 0)
        };
        createPreset("Greeting", greetingCommands, "Wave and perform greeting");
        
        // Patrol sequence
        AICommand[] patrolCommands = {
            new AICommand("movement", "forward", 3000),
            new AICommand("movement", "right", 2000),
            new AICommand("movement", "forward", 3000),
            new AICommand("movement", "left", 2000),
            new AICommand("movement", "stop", 0)
        };
        createPreset("Patrol", patrolCommands, "Basic patrol movement");
        
        // Demo sequence
        AICommand[] demoCommands = {
            new AICommand("servo", "wave", 2000),
            new AICommand("movement", "forward", 2000),
            new AICommand("servo", "point", 2000),
            new AICommand("movement", "backward", 2000),
            new AICommand("servo", "rest", 1000)
        };
        createPreset("Demo", demoCommands, "Demonstration sequence");
    }
    
    private String generatePresetId() {
        return "preset_" + System.currentTimeMillis();
    }
    
    public void cleanup() {
        savePresets();
        Log.d(TAG, "Preset manager cleaned up");
    }
}
