package com.stemrobo.simple.robot;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Simplified Robot Controller for two-wheel robot
 * Handles basic movement commands and servo control
 * Communicates with ESP32 via WiFi and USB Serial
 */
public class SimpleRobotController {
    private static final String TAG = "SimpleRobotController";
    private static final String PREFS_NAME = "robot_settings";
    private static final String KEY_ESP32_IP = "esp32_ip";
    private static final String DEFAULT_ESP32_IP = "***********";
    
    private final Context context;
    private final ExecutorService executorService;
    private ESP32EnhancedComm esp32Comm;
    private SharedPreferences preferences;
    private StatusCallback statusCallback;
    
    // Robot state
    private boolean isConnected = false;
    private String currentMovement = "STOP";
    private float lastDistance = 0.0f;
    
    public interface StatusCallback {
        void onStatusUpdate(String status, boolean isConnected);
    }
    
    public SimpleRobotController(Context context) {
        this.context = context;
        this.executorService = Executors.newSingleThreadExecutor();
        this.preferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
    }
    
    public void initialize() {
        try {
            String esp32IP = preferences.getString(KEY_ESP32_IP, DEFAULT_ESP32_IP);
            esp32Comm = new ESP32EnhancedComm(context);
            esp32Comm.setConnectionCallback(this::onConnectionStatusChanged);
            esp32Comm.setWiFiIP(esp32IP);
            
            // Connect to ESP32
            esp32Comm.connect();

            // Set initial servo speed
            int servoSpeed = preferences.getInt("servo_speed", 50);
            setServoSpeed(servoSpeed);
            
            Log.d(TAG, "Robot controller initialized with ESP32 IP: " + esp32IP);
        } catch (Exception e) {
            Log.e(TAG, "Failed to initialize robot controller", e);
            updateStatus("Initialization failed: " + e.getMessage(), false);
        }
    }
    
    public void setStatusCallback(StatusCallback callback) {
        this.statusCallback = callback;
    }
    
    private void updateStatus(String status, boolean connected) {
        isConnected = connected;
        if (statusCallback != null) {
            statusCallback.onStatusUpdate(status, connected);
        }
        Log.d(TAG, "Status: " + status + " (Connected: " + connected + ")");
    }
    
    private void onConnectionStatusChanged(boolean connected, String message, ESP32EnhancedComm.CommMode mode) {
        updateStatus(message + " (" + mode + ")", connected);
    }
    
    // ===== MOVEMENT COMMANDS =====
    
    public void moveForward() {
        if (esp32Comm != null) {
            esp32Comm.moveForward();
            currentMovement = "FORWARD";
            updateStatus("Moving Forward", true);
        }
    }

    public void moveBackward() {
        if (esp32Comm != null) {
            esp32Comm.moveBackward();
            currentMovement = "BACKWARD";
            updateStatus("Moving Backward", true);
        }
    }

    public void turnLeft() {
        if (esp32Comm != null) {
            esp32Comm.turnLeft();
            currentMovement = "LEFT";
            updateStatus("Turning Left", true);
        }
    }

    public void turnRight() {
        if (esp32Comm != null) {
            esp32Comm.turnRight();
            currentMovement = "RIGHT";
            updateStatus("Turning Right", true);
        }
    }

    public void stopMovement() {
        if (esp32Comm != null) {
            esp32Comm.stopMovement();
            currentMovement = "STOP";
            updateStatus("Robot Stopped", true);
        }
    }

    public void stopAllMovements() {
        if (esp32Comm != null) {
            esp32Comm.stopMovement();
            esp32Comm.moveToRestPosition(); // Stop servos and return to rest
            currentMovement = "STOP";
            updateStatus("All movements stopped", true);
        }
    }
    
    // ===== SERVO COMMANDS =====
    
    public void performWave() {
        if (esp32Comm != null) {
            int angle = getServoAngle("wave", 120);
            executeCommand("WAVE_" + angle, "Waving at " + angle + "°");
        }
    }

    public void performPoint() {
        if (esp32Comm != null) {
            int angle = getServoAngle("point", 180);
            executeCommand("POINT_" + angle, "Pointing at " + angle + "°");
        }
    }

    public void moveToRestPosition() {
        if (esp32Comm != null) {
            esp32Comm.moveToRestPosition();
            updateStatus("Rest Position", true);
        }
    }

    public void performHandshake() {
        if (esp32Comm != null) {
            int angle = getServoAngle("handshake", 90);
            executeCommand("HANDSHAKE_" + angle, "Handshake at " + angle + "°");
        }
    }

    public void setServoSpeed(int speed) {
        // Speed is 0-100, map to a delay value (e.g., 20ms to 1ms)
        // Higher speed = lower delay
        int delay = 20 - (int) (speed / 100.0 * 19);
        executeCommand("SERVO_SPEED_" + delay, "Servo speed set to " + speed + "%");
    }

    private int getServoAngle(String gesture, int defaultAngle) {
        if (context != null) {
            SharedPreferences prefs = context.getSharedPreferences("app_settings", Context.MODE_PRIVATE);
            return prefs.getInt("servo_" + gesture + "_angle", defaultAngle);
        }
        return defaultAngle;
    }

    public void setWalkingAngle(int angle) {
        executeCommand("WALK_ANGLE_" + angle, "Walking angle set to " + angle + "°");
    }

    // ===== INDIVIDUAL SERVO CONTROL =====

    public void setLeftArmPosition(int position) {
        position = Math.max(0, Math.min(180, position)); // Constrain to 0-180
        executeCommand("ARM_LEFT_" + position, "Left Arm Position " + position);
    }

    public void setRightArmPosition(int position) {
        position = Math.max(0, Math.min(180, position)); // Constrain to 0-180
        executeCommand("ARM_RIGHT_" + position, "Right Arm Position " + position);
    }

    public void setHeadPosition(int position) {
        position = Math.max(0, Math.min(180, position)); // Constrain to 0-180
        executeCommand("HEAD_" + position, "Head Position " + position);
    }

    // ===== HEAD MOVEMENT COMMANDS =====

    public void lookLeft() {
        if (esp32Comm != null) {
            esp32Comm.lookLeft();
            updateStatus("Looking Left", true);
        }
    }

    public void lookRight() {
        if (esp32Comm != null) {
            esp32Comm.lookRight();
            updateStatus("Looking Right", true);
        }
    }

    public void lookCenter() {
        if (esp32Comm != null) {
            esp32Comm.lookCenter();
            updateStatus("Looking Center", true);
        }
    }
    
    // ===== SENSOR COMMANDS =====
    
    public void getDistance(DistanceCallback callback) {
        if (esp32Comm != null) {
            // Set up distance callback for ESP32EnhancedComm
            esp32Comm.setDistanceCallback(new ESP32EnhancedComm.DistanceCallback() {
                @Override
                public void onDistanceReceived(float distance) {
                    lastDistance = distance;
                    if (callback != null) {
                        callback.onDistanceReceived(distance);
                    }
                }

                @Override
                public void onDistanceError(String error) {
                    if (callback != null) {
                        callback.onDistanceError(error);
                    }
                }
            });
            esp32Comm.requestDistance();
        } else if (callback != null) {
            callback.onDistanceError("ESP32 not connected");
        }
    }
    
    public void startDistanceStreaming() {
        executeCommand("STREAM_DISTANCE_ON", "Distance streaming enabled");
    }
    
    public void stopDistanceStreaming() {
        executeCommand("STREAM_DISTANCE_OFF", "Distance streaming disabled");
    }
    
    public interface DistanceCallback {
        void onDistanceReceived(float distance);
        void onDistanceError(String error);
    }
    
    // ===== SETTINGS =====
    
    public void updateESP32IP(String newIP) {
        preferences.edit().putString(KEY_ESP32_IP, newIP).apply();
        if (esp32Comm != null) {
            esp32Comm.setWiFiIP(newIP);
        }
        Log.d(TAG, "ESP32 IP updated to: " + newIP);
    }
    
    public String getESP32IP() {
        return preferences.getString(KEY_ESP32_IP, DEFAULT_ESP32_IP);
    }
    
    public void testConnection() {
        if (esp32Comm != null) {
            updateStatus("Testing connection...", false);
            esp32Comm.connect(); // This will trigger the callback with connection status
        }
    }
    
    // ===== PRIVATE METHODS =====
    
    private void executeCommand(String command, String statusMessage) {
        if (esp32Comm == null) {
            updateStatus("ESP32 communication not initialized", false);
            return;
        }
        
        executorService.execute(() -> {
            try {
                esp32Comm.sendCommand(command);
                updateStatus(statusMessage, true);
            } catch (Exception e) {
                Log.e(TAG, "Failed to execute command: " + command, e);
                updateStatus("Command error: " + e.getMessage(), false);
            }
        });
    }
    
    // ===== GETTERS =====
    
    public boolean isConnected() {
        return isConnected;
    }
    
    public String getCurrentMovement() {
        return currentMovement;
    }
    
    public float getLastDistance() {
        return lastDistance;
    }
    
    // ===== CLEANUP =====
    
    public void cleanup() {
        if (esp32Comm != null) {
            esp32Comm.disconnect();
        }
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
        Log.d(TAG, "Robot controller cleaned up");
    }
}
