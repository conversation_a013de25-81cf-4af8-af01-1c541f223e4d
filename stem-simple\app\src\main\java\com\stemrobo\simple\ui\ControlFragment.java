package com.stemrobo.simple.ui;

import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.SeekBar;
import android.widget.TextView;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import com.stemrobo.simple.MainActivity;
import com.stemrobo.simple.R;
import com.stemrobo.simple.robot.SimpleRobotController;

/**
 * Control Fragment for basic robot movement and servo control
 * Features:
 * - Two-wheel robot movement (forward, backward, left, right, stop)
 * - Servo gestures (wave, point, rest, handshake)
 * - Real-time sensor readings (distance, face count)
 * - Manual distance checking
 */
public class ControlFragment extends Fragment {
    private static final String TAG = "ControlFragment";
    
    // UI Components
    private <PERSON><PERSON> btnForward, btnBackward, btnLeft, btnRight, btnStop;
    private Button btnWave, btnPoint, btnRest, btnHandshake;
    private Button btnLookLeft, btnLookCenter, btnLookRight;
    private Button btnCheckDistance;
    private SeekBar seekbarLeftArm, seekbarRightArm, seekbarHead;
    private TextView textLeftArmValue, textRightArmValue, textHeadValue;
    private TextView distanceReading, faceCountReading;
    
    // Robot controller
    private SimpleRobotController robotController;
    
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_control, container, false);
        
        initializeViews(view);
        setupMovementControls();
        setupServoControls();
        setupServoSliders();
        setupHeadControls();
        setupSensorControls();
        
        return view;
    }
    
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        // Get robot controller from MainActivity
        if (getActivity() instanceof MainActivity) {
            robotController = ((MainActivity) getActivity()).getRobotController();
        }
        
        updateSensorReadings();
    }
    
    private void initializeViews(View view) {
        // Movement buttons
        btnForward = view.findViewById(R.id.btn_forward);
        btnBackward = view.findViewById(R.id.btn_backward);
        btnLeft = view.findViewById(R.id.btn_left);
        btnRight = view.findViewById(R.id.btn_right);
        btnStop = view.findViewById(R.id.btn_stop);
        
        // Servo buttons
        btnWave = view.findViewById(R.id.btn_wave);
        btnPoint = view.findViewById(R.id.btn_point);
        btnRest = view.findViewById(R.id.btn_rest);
        btnHandshake = view.findViewById(R.id.btn_handshake);

        // Head control buttons
        btnLookLeft = view.findViewById(R.id.btn_look_left);
        btnLookCenter = view.findViewById(R.id.btn_look_center);
        btnLookRight = view.findViewById(R.id.btn_look_right);

        // Servo sliders
        seekbarLeftArm = view.findViewById(R.id.seekbar_left_arm);
        seekbarRightArm = view.findViewById(R.id.seekbar_right_arm);
        seekbarHead = view.findViewById(R.id.seekbar_head);

        // Servo value displays
        textLeftArmValue = view.findViewById(R.id.text_left_arm_value);
        textRightArmValue = view.findViewById(R.id.text_right_arm_value);
        textHeadValue = view.findViewById(R.id.text_head_value);
        
        // Sensor controls
        btnCheckDistance = view.findViewById(R.id.btn_check_distance);
        distanceReading = view.findViewById(R.id.distance_reading);
        faceCountReading = view.findViewById(R.id.face_count_reading);
    }
    
    private void setupMovementControls() {
        // Forward button - hold to move
        btnForward.setOnTouchListener((v, event) -> {
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    executeMovementCommand("forward");
                    return true;
                case MotionEvent.ACTION_UP:
                case MotionEvent.ACTION_CANCEL:
                    executeMovementCommand("stop");
                    return true;
            }
            return false;
        });
        
        // Backward button - hold to move
        btnBackward.setOnTouchListener((v, event) -> {
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    executeMovementCommand("backward");
                    return true;
                case MotionEvent.ACTION_UP:
                case MotionEvent.ACTION_CANCEL:
                    executeMovementCommand("stop");
                    return true;
            }
            return false;
        });
        
        // Left button - hold to turn
        btnLeft.setOnTouchListener((v, event) -> {
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    executeMovementCommand("left");
                    return true;
                case MotionEvent.ACTION_UP:
                case MotionEvent.ACTION_CANCEL:
                    executeMovementCommand("stop");
                    return true;
            }
            return false;
        });
        
        // Right button - hold to turn
        btnRight.setOnTouchListener((v, event) -> {
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    executeMovementCommand("right");
                    return true;
                case MotionEvent.ACTION_UP:
                case MotionEvent.ACTION_CANCEL:
                    executeMovementCommand("stop");
                    return true;
            }
            return false;
        });
        
        // Stop button - immediate stop
        btnStop.setOnClickListener(v -> executeMovementCommand("stop"));
    }
    
    private void setupServoControls() {
        btnWave.setOnClickListener(v -> executeServoCommand("wave"));
        btnPoint.setOnClickListener(v -> executeServoCommand("point"));
        btnRest.setOnClickListener(v -> executeServoCommand("rest"));
        btnHandshake.setOnClickListener(v -> executeServoCommand("handshake"));
    }

    private void setupServoSliders() {
        // Left arm slider
        seekbarLeftArm.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (fromUser) {
                    textLeftArmValue.setText(progress + "°");
                    if (robotController != null) {
                        robotController.setLeftArmPosition(progress);
                    }
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {}

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {}
        });

        // Right arm slider
        seekbarRightArm.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (fromUser) {
                    textRightArmValue.setText(progress + "°");
                    if (robotController != null) {
                        robotController.setRightArmPosition(progress);
                    }
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {}

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {}
        });

        // Head slider
        seekbarHead.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (fromUser) {
                    textHeadValue.setText(progress + "°");
                    if (robotController != null) {
                        robotController.setHeadPosition(progress);
                    }
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {}

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {}
        });
    }

    private void setupHeadControls() {
        btnLookLeft.setOnClickListener(v -> {
            if (robotController != null) {
                robotController.lookLeft();
                seekbarHead.setProgress(150); // Update slider
                textHeadValue.setText("150°");
            }
        });

        btnLookCenter.setOnClickListener(v -> {
            if (robotController != null) {
                robotController.lookCenter();
                seekbarHead.setProgress(90); // Update slider
                textHeadValue.setText("90°");
            }
        });

        btnLookRight.setOnClickListener(v -> {
            if (robotController != null) {
                robotController.lookRight();
                seekbarHead.setProgress(30); // Update slider
                textHeadValue.setText("30°");
            }
        });
    }
    
    private void setupSensorControls() {
        btnCheckDistance.setOnClickListener(v -> checkDistance());
    }
    
    private void executeMovementCommand(String command) {
        if (robotController == null) {
            showError("Robot controller not available");
            return;
        }

        if (command == null || command.trim().isEmpty()) {
            showError("Invalid command");
            return;
        }

        try {
            switch (command.toLowerCase().trim()) {
                case "forward":
                    robotController.moveForward();
                    break;
                case "backward":
                    robotController.moveBackward();
                    break;
                case "left":
                    robotController.turnLeft();
                    break;
                case "right":
                    robotController.turnRight();
                    break;
                case "stop":
                    robotController.stopMovement();
                    break;
                default:
                    showError("Unknown movement command: " + command);
                    return;
            }

            // Visual feedback
            highlightActiveButton(command);

        } catch (Exception e) {
            Log.e(TAG, "Movement command failed: " + command, e);
            showError("Movement command failed: " + e.getMessage());
        }
    }
    
    private void executeServoCommand(String command) {
        if (robotController == null) {
            showError("Robot controller not available");
            return;
        }
        
        try {
            switch (command.toLowerCase()) {
                case "wave":
                    robotController.performWave();
                    showMessage("Waving...");
                    break;
                case "point":
                    robotController.performPoint();
                    showMessage("Pointing...");
                    break;
                case "rest":
                    robotController.moveToRestPosition();
                    showMessage("Moving to rest position...");
                    break;
                case "handshake":
                    robotController.performHandshake();
                    showMessage("Performing handshake...");
                    break;
                default:
                    showError("Unknown servo command: " + command);
            }
            
        } catch (Exception e) {
            showError("Servo command failed: " + e.getMessage());
        }
    }
    
    private void checkDistance() {
        if (robotController == null) {
            showError("Robot controller not available");
            return;
        }
        
        robotController.getDistance(new SimpleRobotController.DistanceCallback() {
            @Override
            public void onDistanceReceived(float distance) {
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        distanceReading.setText(String.format("Distance: %.1f cm", distance));
                    });
                }
            }
            
            @Override
            public void onDistanceError(String error) {
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        distanceReading.setText("Distance: Error");
                        showError("Distance check failed: " + error);
                    });
                }
            }
        });
    }
    
    private void highlightActiveButton(String command) {
        // Reset all button backgrounds
        resetButtonHighlights();
        
        // Highlight active button
        switch (command.toLowerCase()) {
            case "forward":
                btnForward.setBackgroundColor(getResources().getColor(R.color.accent_orange, null));
                break;
            case "backward":
                btnBackward.setBackgroundColor(getResources().getColor(R.color.accent_orange, null));
                break;
            case "left":
                btnLeft.setBackgroundColor(getResources().getColor(R.color.accent_orange, null));
                break;
            case "right":
                btnRight.setBackgroundColor(getResources().getColor(R.color.accent_orange, null));
                break;
            case "stop":
                resetButtonHighlights();
                break;
        }
    }
    
    private void resetButtonHighlights() {
        int defaultColor = getResources().getColor(R.color.button_primary, null);
        btnForward.setBackgroundColor(defaultColor);
        btnBackward.setBackgroundColor(defaultColor);
        btnLeft.setBackgroundColor(defaultColor);
        btnRight.setBackgroundColor(defaultColor);
    }
    
    private void updateSensorReadings() {
        // Update distance reading
        if (robotController != null) {
            float lastDistance = robotController.getLastDistance();
            distanceReading.setText(String.format("Distance: %.1f cm", lastDistance));
        }
        
        // Update face count (will be updated by MainActivity)
        faceCountReading.setText("Faces: 0");
    }
    
    public void updateFaceCount(int count) {
        if (faceCountReading != null) {
            faceCountReading.setText(String.format("Faces: %d", count));
        }
    }
    
    public void updateDistance(float distance) {
        if (distanceReading != null) {
            distanceReading.setText(String.format("Distance: %.1f cm", distance));
        }
    }
    
    private void showMessage(String message) {
        if (getContext() != null) {
            Toast.makeText(getContext(), message, Toast.LENGTH_SHORT).show();
        }
    }
    
    private void showError(String error) {
        if (getContext() != null) {
            Toast.makeText(getContext(), "Error: " + error, Toast.LENGTH_LONG).show();
        }
    }
    
    @Override
    public void onDestroyView() {
        super.onDestroyView();
        // Stop any ongoing movement when fragment is destroyed
        if (robotController != null) {
            robotController.stopMovement();
        }
    }
}
