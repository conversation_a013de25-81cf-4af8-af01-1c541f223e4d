<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="8dp">

    <!-- AI Chat Display -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="2"
        android:layout_marginBottom="8dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="AI Chat"
                android:textSize="14sp"
                android:textStyle="bold"
                android:layout_marginBottom="8dp" />

            <!-- Chat messages recycler view -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/chat_recycler_view"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:scrollbars="vertical" />

            <!-- Compact Status Bar -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <View
                    android:id="@+id/listening_indicator"
                    android:layout_width="8dp"
                    android:layout_height="8dp"
                    android:layout_marginEnd="6dp"
                    android:background="@drawable/circle_shape"
                    android:backgroundTint="@color/indicator_inactive" />

                <TextView
                    android:id="@+id/ai_status"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Ready"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp" />

                <!-- Emergency Stop Button -->
                <Button
                    android:id="@+id/btn_emergency_stop"
                    android:layout_width="wrap_content"
                    android:layout_height="28dp"
                    android:text="🛑"
                    android:textSize="10sp"
                    android:background="@color/button_danger"
                    android:textColor="@android:color/white"
                    android:minWidth="0dp"
                    android:paddingStart="6dp"
                    android:paddingEnd="6dp" />

            </LinearLayout>

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Scrollable Settings Section -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Voice Control Settings -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/voice_settings"
                android:textSize="16sp"
                android:textStyle="bold"
                android:layout_marginBottom="12dp" />

            <!-- Auto Voice Recognition Toggle -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="8dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/auto_voice_recognition"
                    android:textSize="14sp" />

                <com.google.android.material.switchmaterial.SwitchMaterial
                    android:id="@+id/switch_auto_voice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="true" />

            </LinearLayout>

            <!-- Language Selection -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/language"
                    android:textSize="14sp" />

                <Spinner
                    android:id="@+id/spinner_ai_language"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:minWidth="120dp" />

            </LinearLayout>

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Input Section -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Text input -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="@string/type_command"
                app:boxStrokeColor="@color/primary_blue"
                app:hintTextColor="@color/primary_blue">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/command_input"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:imeOptions="actionSend"
                    android:inputType="textMultiLine"
                    android:maxLines="3" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Control buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/btn_voice_input"
                    style="@style/ButtonPrimary"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:text="🎤 Voice" />

                <Button
                    android:id="@+id/btn_send_text"
                    style="@style/ButtonPrimary"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:text="@string/send" />

            </LinearLayout>

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Chat Controls -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="8dp">

        <Button
            android:id="@+id/btn_clear_chat"
            style="@style/ButtonSecondary"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Clear Chat" />

        </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</LinearLayout>
