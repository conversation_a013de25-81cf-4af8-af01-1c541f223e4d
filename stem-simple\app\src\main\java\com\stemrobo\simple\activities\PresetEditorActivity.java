package com.stemrobo.simple.activities;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.ArrayAdapter;
import android.widget.Toast;
import android.widget.TextView;
import android.widget.LinearLayout;
import android.util.Log;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.app.AlertDialog;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.ItemTouchHelper;

import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.stemrobo.simple.R;
import com.stemrobo.simple.ai.*;
import com.stemrobo.simple.adapters.CommandEditorAdapter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Activity for creating and editing robot presets
 */
public class PresetEditorActivity extends AppCompatActivity {
    private static final String TAG = "PresetEditorActivity";
    
    // Intent extras
    public static final String EXTRA_PRESET_MODE = "preset_mode";
    public static final String EXTRA_PRESET_ID = "preset_id";
    
    // Modes
    public static final int MODE_CREATE = 1;
    public static final int MODE_EDIT = 2;
    public static final int MODE_DUPLICATE = 3;
    
    // UI Components
    private EditText editPresetName;
    private EditText editPresetDescription;
    private Spinner spinnerPresetCategory;
    private TextView textTotalDuration;
    private LinearLayout timelineContainer;
    private View layoutEmptySteps;
    private RecyclerView commandsRecyclerView;
    private FloatingActionButton fabAddCommand;
    private Button btnSavePreset;
    private Button btnTestPreset;
    private Button btnCancelPreset;
    private Button btnPreviewPreset;
    
    // Command Editor Components
    private Spinner spinnerCommandType;
    private Spinner spinnerCommandAction;
    private EditText editCommandDuration;
    private EditText editCommandParameter;
    private Button btnAddCommandToList;
    
    // Data
    private List<AICommand> commands;
    private CommandEditorAdapter commandAdapter;
    private PresetManager presetManager;
    private CommandParser commandParser;
    private int currentMode;
    private String editingPresetId;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_preset_editor);
        
        initializeViews();
        initializeData();
        setupRecyclerView();
        setupSpinners();
        setupButtonListeners();
        
        // Handle intent data
        handleIntentData();
        
        Log.d(TAG, "Preset editor activity created");
    }
    
    private void initializeViews() {
        editPresetName = findViewById(R.id.edit_preset_name);
        editPresetDescription = findViewById(R.id.edit_preset_description);
        commandsRecyclerView = findViewById(R.id.commands_recycler_view);
        fabAddCommand = findViewById(R.id.fab_add_command);
        btnSavePreset = findViewById(R.id.btn_save_preset);
        btnTestPreset = findViewById(R.id.btn_test_preset);
        btnCancelPreset = findViewById(R.id.btn_cancel_preset);
        
        // Command editor views
        spinnerCommandType = findViewById(R.id.spinner_command_type);
        spinnerCommandAction = findViewById(R.id.spinner_command_action);
        editCommandDuration = findViewById(R.id.edit_command_duration);
        editCommandParameter = findViewById(R.id.edit_command_parameter);
        btnAddCommandToList = findViewById(R.id.btn_add_command_to_list);
    }
    
    private void initializeData() {
        commands = new ArrayList<>();
        commandParser = new CommandParser(null, null); // Robot controller will be set later
        presetManager = new PresetManager(this, commandParser);
    }
    
    private void setupRecyclerView() {
        commandAdapter = new CommandEditorAdapter(commands, new CommandEditorAdapter.CommandActionListener() {
            @Override
            public void onCommandEdit(AICommand command, int position) {
                editCommand(command, position);
            }

            @Override
            public void onCommandDelete(AICommand command, int position) {
                deleteCommand(position);
            }

            @Override
            public void onCommandMoveUp(AICommand command, int position) {
                moveCommand(position, position - 1);
            }

            @Override
            public void onCommandMoveDown(AICommand command, int position) {
                moveCommand(position, position + 1);
            }
        });
        
        commandsRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        commandsRecyclerView.setAdapter(commandAdapter);
    }
    
    private void setupSpinners() {
        // Command types
        String[] commandTypes = {"movement", "servo", "sensor", "greeting", "delay"};
        ArrayAdapter<String> typeAdapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, commandTypes);
        typeAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerCommandType.setAdapter(typeAdapter);
        
        // Command actions (will be updated based on type selection)
        updateActionSpinner("movement");
        
        // Set up type selection listener
        spinnerCommandType.setOnItemSelectedListener(new android.widget.AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(android.widget.AdapterView<?> parent, View view, int position, long id) {
                String selectedType = commandTypes[position];
                updateActionSpinner(selectedType);
            }
            
            @Override
            public void onNothingSelected(android.widget.AdapterView<?> parent) {}
        });
    }
    
    private void updateActionSpinner(String commandType) {
        String[] actions;
        
        switch (commandType.toLowerCase()) {
            case "movement":
                actions = new String[]{"forward", "backward", "left", "right", "stop"};
                break;
            case "servo":
                actions = new String[]{"wave", "point", "handshake", "rest", "custom"};
                break;
            case "sensor":
                actions = new String[]{"get_distance", "start_distance_stream", "stop_distance_stream"};
                break;
            case "greeting":
                actions = new String[]{"trigger_greeting", "wave_greeting", "handshake_greeting"};
                break;
            case "delay":
                actions = new String[]{"wait"};
                break;
            default:
                actions = new String[]{"unknown"};
                break;
        }
        
        ArrayAdapter<String> actionAdapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, actions);
        actionAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerCommandAction.setAdapter(actionAdapter);
    }
    
    private void setupButtonListeners() {
        fabAddCommand.setOnClickListener(v -> showAddCommandDialog());
        btnAddCommandToList.setOnClickListener(v -> addCommandToList());
        btnSavePreset.setOnClickListener(v -> savePreset());
        btnTestPreset.setOnClickListener(v -> testPreset());
        btnCancelPreset.setOnClickListener(v -> finish());

        // Add step management button if it exists
        Button btnAddStep = findViewById(R.id.btn_add_step);
        if (btnAddStep != null) {
            btnAddStep.setOnClickListener(v -> addNewStep());
        }
    }
    
    private void handleIntentData() {
        Intent intent = getIntent();
        currentMode = intent.getIntExtra(EXTRA_PRESET_MODE, MODE_CREATE);
        editingPresetId = intent.getStringExtra(EXTRA_PRESET_ID);
        
        switch (currentMode) {
            case MODE_CREATE:
                setTitle("Create New Preset");
                break;
            case MODE_EDIT:
                setTitle("Edit Preset");
                loadPresetForEditing();
                break;
            case MODE_DUPLICATE:
                setTitle("Duplicate Preset");
                loadPresetForDuplication();
                break;
        }
    }
    
    private void loadPresetForEditing() {
        if (editingPresetId != null) {
            RobotPreset preset = presetManager.getPresetById(editingPresetId);
            if (preset != null) {
                editPresetName.setText(preset.getName());
                editPresetDescription.setText(preset.getDescription());
                commands.clear();
                commands.addAll(Arrays.asList(preset.getCommands()));
                commandAdapter.notifyDataSetChanged();
            }
        }
    }
    
    private void loadPresetForDuplication() {
        if (editingPresetId != null) {
            RobotPreset preset = presetManager.getPresetById(editingPresetId);
            if (preset != null) {
                editPresetName.setText(preset.getName() + " (Copy)");
                editPresetDescription.setText(preset.getDescription());
                commands.clear();
                commands.addAll(Arrays.asList(preset.getCommands()));
                commandAdapter.notifyDataSetChanged();
            }
        }
    }
    
    private void showAddCommandDialog() {
        // Toggle visibility of command editor section
        View commandEditor = findViewById(R.id.command_editor_section);
        if (commandEditor.getVisibility() == View.VISIBLE) {
            commandEditor.setVisibility(View.GONE);
        } else {
            commandEditor.setVisibility(View.VISIBLE);
        }
    }
    
    private void addCommandToList() {
        String type = spinnerCommandType.getSelectedItem().toString();
        String action = spinnerCommandAction.getSelectedItem().toString();
        String durationStr = editCommandDuration.getText().toString();
        String parameter = editCommandParameter.getText().toString();
        
        int duration = 0;
        if (!durationStr.isEmpty()) {
            try {
                duration = Integer.parseInt(durationStr);
            } catch (NumberFormatException e) {
                Toast.makeText(this, "Invalid duration", Toast.LENGTH_SHORT).show();
                return;
            }
        }
        
        AICommand command = new AICommand(type, action, duration, parameter.isEmpty() ? null : parameter);
        commands.add(command);
        commandAdapter.notifyItemInserted(commands.size() - 1);
        updateCommandCount();

        // Clear input fields
        editCommandDuration.setText("");
        editCommandParameter.setText("");
        
        // Hide command editor
        findViewById(R.id.command_editor_section).setVisibility(View.GONE);
        
        Toast.makeText(this, "Command added", Toast.LENGTH_SHORT).show();
    }
    
    private void editCommand(AICommand command, int position) {
        // Populate editor with command data
        spinnerCommandType.setSelection(getTypeIndex(command.getType()));
        updateActionSpinner(command.getType());
        spinnerCommandAction.setSelection(getActionIndex(command.getAction(), command.getType()));
        editCommandDuration.setText(String.valueOf(command.getDuration()));
        editCommandParameter.setText(command.getParameter() != null ? command.getParameter() : "");
        
        // Show editor
        findViewById(R.id.command_editor_section).setVisibility(View.VISIBLE);
        
        // Remove the command from list (will be re-added when user clicks add)
        commands.remove(position);
        commandAdapter.notifyItemRemoved(position);
    }
    
    private void deleteCommand(int position) {
        commands.remove(position);
        commandAdapter.notifyItemRemoved(position);
        updateCommandCount();
        Toast.makeText(this, "Command deleted", Toast.LENGTH_SHORT).show();
    }
    
    private void moveCommand(int fromPosition, int toPosition) {
        if (toPosition >= 0 && toPosition < commands.size()) {
            AICommand command = commands.remove(fromPosition);
            commands.add(toPosition, command);
            commandAdapter.notifyItemMoved(fromPosition, toPosition);
        }
    }
    
    private int getTypeIndex(String type) {
        String[] types = {"movement", "servo", "sensor", "greeting", "delay"};
        for (int i = 0; i < types.length; i++) {
            if (types[i].equals(type)) {
                return i;
            }
        }
        return 0;
    }
    
    private int getActionIndex(String action, String type) {
        String[] actions;
        switch (type.toLowerCase()) {
            case "movement":
                actions = new String[]{"forward", "backward", "left", "right", "stop"};
                break;
            case "servo":
                actions = new String[]{"wave", "point", "handshake", "rest", "custom"};
                break;
            case "sensor":
                actions = new String[]{"get_distance", "start_distance_stream", "stop_distance_stream"};
                break;
            case "greeting":
                actions = new String[]{"trigger_greeting", "wave_greeting", "handshake_greeting"};
                break;
            case "delay":
                actions = new String[]{"wait"};
                break;
            default:
                return 0;
        }
        
        for (int i = 0; i < actions.length; i++) {
            if (actions[i].equals(action)) {
                return i;
            }
        }
        return 0;
    }
    
    private void savePreset() {
        String name = editPresetName.getText().toString().trim();
        String description = editPresetDescription.getText().toString().trim();
        
        if (name.isEmpty()) {
            Toast.makeText(this, "Please enter a preset name", Toast.LENGTH_SHORT).show();
            return;
        }
        
        if (commands.isEmpty()) {
            Toast.makeText(this, "Please add at least one command", Toast.LENGTH_SHORT).show();
            return;
        }
        
        AICommand[] commandArray = commands.toArray(new AICommand[0]);
        
        if (currentMode == MODE_EDIT && editingPresetId != null) {
            // Update existing preset
            presetManager.updatePreset(editingPresetId, name, description, commandArray);
        } else {
            // Create new preset
            presetManager.createPreset(name, commandArray, description);
        }
        
        setResult(RESULT_OK);
        finish();
        
        Toast.makeText(this, "Preset saved successfully", Toast.LENGTH_SHORT).show();
    }
    
    private void testPreset() {
        if (commands.isEmpty()) {
            Toast.makeText(this, "No commands to test", Toast.LENGTH_SHORT).show();
            return;
        }
        
        AICommand[] commandArray = commands.toArray(new AICommand[0]);
        commandParser.executeCommands(commandArray);
        
        Toast.makeText(this, "Testing preset...", Toast.LENGTH_SHORT).show();
    }

    private void addNewStep() {
        showStepCreationDialog();
    }

    private void showStepCreationDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("Create New Step");

        // Create a simple input dialog
        LinearLayout layout = new LinearLayout(this);
        layout.setOrientation(LinearLayout.VERTICAL);
        layout.setPadding(50, 40, 50, 10);

        EditText editStepName = new EditText(this);
        editStepName.setHint("Step name");
        editStepName.setText("Step " + (commands.size() + 1));
        layout.addView(editStepName);

        EditText editStepDuration = new EditText(this);
        editStepDuration.setHint("Duration (ms)");
        editStepDuration.setText("2000");
        editStepDuration.setInputType(android.text.InputType.TYPE_CLASS_NUMBER);
        layout.addView(editStepDuration);

        builder.setView(layout);
        builder.setPositiveButton("Create", (dialog, which) -> {
            String stepName = editStepName.getText().toString().trim();
            String durationStr = editStepDuration.getText().toString().trim();

            if (stepName.isEmpty()) {
                stepName = "Step " + (commands.size() + 1);
            }

            int duration = 2000; // Default 2 seconds
            try {
                duration = Integer.parseInt(durationStr);
            } catch (NumberFormatException e) {
                // Use default
            }

            // Create a step command
            AICommand stepCommand = new AICommand("step", stepName, duration, "Timeline step: " + stepName);
            commands.add(stepCommand);
            commandAdapter.notifyItemInserted(commands.size() - 1);
            updateCommandCount();
        });

        builder.setNegativeButton("Cancel", null);
        builder.show();
    }

    private void updateCommandCount() {
        TextView commandCount = findViewById(R.id.command_count);
        TextView totalDuration = findViewById(R.id.text_total_duration);

        if (commandCount != null) {
            commandCount.setText(commands.size() + " steps");
        }

        if (totalDuration != null) {
            int total = 0;
            for (AICommand cmd : commands) {
                total += cmd.getDuration();
            }
            totalDuration.setText((total / 1000) + "s");
        }

        // Show/hide empty state
        View emptyState = findViewById(R.id.empty_commands_state);
        if (emptyState != null) {
            emptyState.setVisibility(commands.isEmpty() ? View.VISIBLE : View.GONE);
        }

        commandsRecyclerView.setVisibility(commands.isEmpty() ? View.GONE : View.VISIBLE);
    }
}
