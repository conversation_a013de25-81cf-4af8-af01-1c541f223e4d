package com.stemrobo.simple.ai;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * Robot Preset class for STEM Simple Robot
 * Represents a saved sequence of commands that can be executed
 */
public class RobotPreset {
    private String id;
    private String name;
    private String description;
    private AICommand[] commands;
    private long createdTime;
    private long lastExecuted;
    
    public RobotPreset() {
        // Default constructor for JSON parsing
    }
    
    public RobotPreset(String id, String name, String description, AICommand[] commands, long createdTime) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.commands = commands;
        this.createdTime = createdTime;
        this.lastExecuted = 0;
    }
    
    // Getters
    public String getId() {
        return id;
    }
    
    public String getName() {
        return name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public AICommand[] getCommands() {
        return commands;
    }
    
    public long getCreatedTime() {
        return createdTime;
    }
    
    public long getLastExecuted() {
        return lastExecuted;
    }
    
    // Setters
    public void setId(String id) {
        this.id = id;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public void setCommands(AICommand[] commands) {
        this.commands = commands;
    }
    
    public void setCreatedTime(long createdTime) {
        this.createdTime = createdTime;
    }
    
    public void setLastExecuted(long lastExecuted) {
        this.lastExecuted = lastExecuted;
    }
    
    // Utility methods
    public int getCommandCount() {
        return commands != null ? commands.length : 0;
    }
    
    public boolean hasBeenExecuted() {
        return lastExecuted > 0;
    }
    
    public String getCreatedTimeFormatted() {
        SimpleDateFormat sdf = new SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault());
        return sdf.format(new Date(createdTime));
    }
    
    public String getLastExecutedFormatted() {
        if (lastExecuted == 0) {
            return "Never";
        }
        SimpleDateFormat sdf = new SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault());
        return sdf.format(new Date(lastExecuted));
    }
    
    public long getTimeSinceCreated() {
        return System.currentTimeMillis() - createdTime;
    }
    
    public long getTimeSinceLastExecuted() {
        if (lastExecuted == 0) {
            return -1; // Never executed
        }
        return System.currentTimeMillis() - lastExecuted;
    }
    
    /**
     * Get estimated execution time in milliseconds
     */
    public long getEstimatedExecutionTime() {
        if (commands == null) {
            return 0;
        }
        
        long totalTime = 0;
        for (AICommand command : commands) {
            if (command.hasDuration()) {
                totalTime += command.getDuration();
            } else {
                // Default time for commands without duration
                switch (command.getType().toLowerCase()) {
                    case "movement":
                        totalTime += 3000; // 3 seconds default for movement
                        break;
                    case "servo":
                        totalTime += 2000; // 2 seconds default for servo
                        break;
                    case "sensor":
                        totalTime += 1000; // 1 second default for sensor
                        break;
                    case "greeting":
                        totalTime += 5000; // 5 seconds default for greeting
                        break;
                    default:
                        totalTime += 1000; // 1 second default
                        break;
                }
            }
        }
        
        return totalTime;
    }
    
    /**
     * Get formatted estimated execution time
     */
    public String getEstimatedExecutionTimeFormatted() {
        long timeMs = getEstimatedExecutionTime();
        if (timeMs < 1000) {
            return timeMs + "ms";
        } else if (timeMs < 60000) {
            return (timeMs / 1000) + "s";
        } else {
            long minutes = timeMs / 60000;
            long seconds = (timeMs % 60000) / 1000;
            return minutes + "m " + seconds + "s";
        }
    }
    
    /**
     * Get command summary
     */
    public String getCommandSummary() {
        if (commands == null || commands.length == 0) {
            return "No commands";
        }
        
        StringBuilder summary = new StringBuilder();
        for (int i = 0; i < commands.length; i++) {
            AICommand command = commands[i];
            summary.append(command.getAction());
            if (i < commands.length - 1) {
                summary.append(" → ");
            }
        }
        
        return summary.toString();
    }
    
    /**
     * Validate preset
     */
    public boolean isValid() {
        return id != null && !id.trim().isEmpty() &&
               name != null && !name.trim().isEmpty() &&
               commands != null && commands.length > 0 &&
               createdTime > 0;
    }
    
    @Override
    public String toString() {
        return "RobotPreset{" +
               "id='" + id + '\'' +
               ", name='" + name + '\'' +
               ", commandCount=" + getCommandCount() +
               ", created=" + getCreatedTimeFormatted() +
               ", lastExecuted=" + getLastExecutedFormatted() +
               '}';
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        RobotPreset that = (RobotPreset) obj;
        return id != null ? id.equals(that.id) : that.id == null;
    }
    
    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }
}
