package com.stemrobo.simple.activities;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.view.View;
import android.view.WindowManager;
import android.webkit.WebChromeClient;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.ImageButton;
import android.widget.ProgressBar;
import android.widget.Toast;
import android.util.Log;

import androidx.appcompat.app.AppCompatActivity;

import com.stemrobo.simple.R;
import com.stemrobo.simple.utils.VideoStopManager;

/**
 * Activity for playing YouTube LMS class videos in fullscreen
 */
public class LMSYouTubePlayerActivity extends AppCompatActivity {
    private static final String TAG = "LMSYouTubePlayer";
    
    public static final String EXTRA_YOUTUBE_URL = "youtube_url";
    public static final String EXTRA_CLASS_NUMBER = "class_number";
    
    // Default YouTube video for all classes (will be customized later)
    private static final String DEFAULT_YOUTUBE_URL = "https://www.youtube.com/watch?v=8a2HGrk7MhM";
    
    private WebView webView;
    private ImageButton closeButton;
    private ProgressBar progressBar;
    private boolean isVideoStopped = false;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // Enable fullscreen mode
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN);
        
        // Hide action bar
        if (getSupportActionBar() != null) {
            getSupportActionBar().hide();
        }
        
        setContentView(R.layout.activity_lms_youtube_player);
        
        initializeViews();
        setupWebView();
        
        // Get YouTube URL and class number from intent
        String youtubeUrl = getIntent().getStringExtra(EXTRA_YOUTUBE_URL);
        int classNumber = getIntent().getIntExtra(EXTRA_CLASS_NUMBER, 0);
        
        if (youtubeUrl != null) {
            loadYouTubeVideo(youtubeUrl);
        } else {
            // Use default URL for the specified class
            loadYouTubeVideo(getYouTubeUrlForClass(classNumber));
        }
        
        Log.d(TAG, "LMS YouTube Player started for class: " + classNumber);
        
        // Register with VideoStopManager for voice stop commands
        VideoStopManager.getInstance().setCurrentVideoCallback(() -> {
            Log.d(TAG, "Stop command received via voice");
            stopVideo();
            finish();
        });
    }
    
    private void initializeViews() {
        webView = findViewById(R.id.webview);
        closeButton = findViewById(R.id.close_button);
        progressBar = findViewById(R.id.progress_bar);
        
        // Setup close button
        closeButton.setOnClickListener(v -> {
            Log.d(TAG, "Close button clicked");
            stopVideo();
            finish();
        });
        
        // Hide system UI for immersive experience
        hideSystemUI();
    }
    
    @SuppressLint("SetJavaScriptEnabled")
    private void setupWebView() {
        WebSettings webSettings = webView.getSettings();
        webSettings.setJavaScriptEnabled(true);
        webSettings.setDomStorageEnabled(true);
        webSettings.setLoadWithOverviewMode(true);
        webSettings.setUseWideViewPort(true);
        webSettings.setBuiltInZoomControls(false);
        webSettings.setDisplayZoomControls(false);
        webSettings.setSupportZoom(false);
        webSettings.setDefaultTextEncodingName("utf-8");
        
        // Enable hardware acceleration for better video performance
        webSettings.setRenderPriority(WebSettings.RenderPriority.HIGH);
        webSettings.setCacheMode(WebSettings.LOAD_DEFAULT);
        
        // Setup WebView client
        webView.setWebViewClient(new WebViewClient() {
            @Override
            public void onPageStarted(WebView view, String url, android.graphics.Bitmap favicon) {
                super.onPageStarted(view, url, favicon);
                progressBar.setVisibility(View.VISIBLE);
            }
            
            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                progressBar.setVisibility(View.GONE);
                Log.d(TAG, "YouTube page loaded successfully");
            }
            
            @Override
            public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
                super.onReceivedError(view, errorCode, description, failingUrl);
                Log.e(TAG, "WebView error: " + description);
                progressBar.setVisibility(View.GONE);
                Toast.makeText(LMSYouTubePlayerActivity.this, 
                    "Error loading video", Toast.LENGTH_SHORT).show();
            }
        });
        
        // Setup WebChrome client for fullscreen video support
        webView.setWebChromeClient(new WebChromeClient() {
            @Override
            public void onProgressChanged(WebView view, int newProgress) {
                super.onProgressChanged(view, newProgress);
                progressBar.setProgress(newProgress);
            }
        });
    }
    
    private void loadYouTubeVideo(String youtubeUrl) {
        try {
            // Convert YouTube watch URL to embed URL for better mobile experience
            String embedUrl = convertToEmbedUrl(youtubeUrl);
            
            // Create HTML content with responsive YouTube embed
            String htmlContent = createYouTubeEmbedHtml(embedUrl);
            
            // Load the HTML content
            webView.loadDataWithBaseURL("https://www.youtube.com", htmlContent, "text/html", "UTF-8", null);
            
            Log.d(TAG, "Loading YouTube embed: " + embedUrl);
            
        } catch (Exception e) {
            Log.e(TAG, "Error loading YouTube video", e);
            Toast.makeText(this, "Error loading video", Toast.LENGTH_SHORT).show();
            finish();
        }
    }
    
    private String convertToEmbedUrl(String youtubeUrl) {
        // Extract video ID from various YouTube URL formats
        String videoId = "";
        
        if (youtubeUrl.contains("watch?v=")) {
            videoId = youtubeUrl.split("watch\\?v=")[1].split("&")[0];
        } else if (youtubeUrl.contains("youtu.be/")) {
            videoId = youtubeUrl.split("youtu.be/")[1].split("\\?")[0];
        } else if (youtubeUrl.contains("embed/")) {
            videoId = youtubeUrl.split("embed/")[1].split("\\?")[0];
        }
        
        return "https://www.youtube.com/embed/" + videoId + "?autoplay=1&fs=1&rel=0";
    }
    
    private String createYouTubeEmbedHtml(String embedUrl) {
        return "<!DOCTYPE html>" +
                "<html>" +
                "<head>" +
                "<meta charset='UTF-8'>" +
                "<meta name='viewport' content='width=device-width, initial-scale=1.0'>" +
                "<style>" +
                "body { margin: 0; padding: 0; background: #000; }" +
                "iframe { width: 100vw; height: 100vh; border: none; }" +
                "</style>" +
                "</head>" +
                "<body>" +
                "<iframe src='" + embedUrl + "' " +
                "frameborder='0' " +
                "allow='accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture' " +
                "allowfullscreen>" +
                "</iframe>" +
                "</body>" +
                "</html>";
    }
    
    private String getYouTubeUrlForClass(int classNumber) {
        // Map class numbers to specific YouTube URLs
        // For now, using default URL for all classes
        // TODO: Replace with actual class-specific URLs
        switch (classNumber) {
            case 1: return "https://www.youtube.com/watch?v=8a2HGrk7MhM"; // Class 1
            case 2: return "https://www.youtube.com/watch?v=8a2HGrk7MhM"; // Class 2
            case 3: return "https://www.youtube.com/watch?v=8a2HGrk7MhM"; // Class 3
            // Add more classes as needed
            default: return DEFAULT_YOUTUBE_URL;
        }
    }
    
    public void stopVideo() {
        if (webView != null && !isVideoStopped) {
            Log.d(TAG, "Stopping video playback");
            webView.loadUrl("about:blank");
            isVideoStopped = true;
        }
    }
    
    private void hideSystemUI() {
        View decorView = getWindow().getDecorView();
        decorView.setSystemUiVisibility(
            View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
            | View.SYSTEM_UI_FLAG_LAYOUT_STABLE
            | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
            | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
            | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
            | View.SYSTEM_UI_FLAG_FULLSCREEN);
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (webView != null) {
            webView.destroy();
        }
        // Clear video stop callback
        VideoStopManager.getInstance().clearCurrentVideoCallback();
    }
    
    @Override
    protected void onPause() {
        super.onPause();
        if (webView != null) {
            webView.onPause();
        }
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        if (webView != null && !isVideoStopped) {
            webView.onResume();
        }
        hideSystemUI();
    }
    
    @Override
    public void onBackPressed() {
        Log.d(TAG, "Back button pressed");
        stopVideo();
        super.onBackPressed();
    }
}
