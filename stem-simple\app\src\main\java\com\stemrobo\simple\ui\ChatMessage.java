package com.stemrobo.simple.ui;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * Chat Message class for AI chat interface
 */
public class ChatMessage {
    public enum Type {
        USER,    // User input
        AI,      // AI response
        SYSTEM   // System messages
    }
    
    private String message;
    private Type type;
    private long timestamp;
    
    public ChatMessage(String message, Type type) {
        this.message = message;
        this.type = type;
        this.timestamp = System.currentTimeMillis();
    }
    
    public ChatMessage(String message, Type type, long timestamp) {
        this.message = message;
        this.type = type;
        this.timestamp = timestamp;
    }
    
    // Getters
    public String getMessage() {
        return message;
    }
    
    public Type getType() {
        return type;
    }
    
    public long getTimestamp() {
        return timestamp;
    }
    
    // Setters
    public void setMessage(String message) {
        this.message = message;
    }
    
    public void setType(Type type) {
        this.type = type;
    }
    
    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }
    
    // Utility methods
    public String getFormattedTime() {
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm", Locale.getDefault());
        return sdf.format(new Date(timestamp));
    }
    
    public String getFormattedDateTime() {
        SimpleDateFormat sdf = new SimpleDateFormat("MMM dd, HH:mm", Locale.getDefault());
        return sdf.format(new Date(timestamp));
    }
    
    public boolean isUserMessage() {
        return type == Type.USER;
    }
    
    public boolean isAIMessage() {
        return type == Type.AI;
    }
    
    public boolean isSystemMessage() {
        return type == Type.SYSTEM;
    }
    
    @Override
    public String toString() {
        return "ChatMessage{" +
               "message='" + message + '\'' +
               ", type=" + type +
               ", timestamp=" + getFormattedDateTime() +
               '}';
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        ChatMessage that = (ChatMessage) obj;
        
        if (timestamp != that.timestamp) return false;
        if (message != null ? !message.equals(that.message) : that.message != null) return false;
        return type == that.type;
    }
    
    @Override
    public int hashCode() {
        int result = message != null ? message.hashCode() : 0;
        result = 31 * result + (type != null ? type.hashCode() : 0);
        result = 31 * result + (int) (timestamp ^ (timestamp >>> 32));
        return result;
    }
}
