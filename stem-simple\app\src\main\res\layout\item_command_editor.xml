<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp"
        android:gravity="center_vertical">

        <!-- Command Number -->
        <TextView
            android:id="@+id/command_number"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:background="@drawable/circle_shape"
            android:backgroundTint="@color/primary_blue"
            android:text="1"
            android:textColor="@android:color/white"
            android:textSize="14sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginEnd="12dp" />

        <!-- Command Info -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- Type and Action -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="4dp">

                <TextView
                    android:id="@+id/command_type"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="movement"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:background="@drawable/tag_background"
                    android:backgroundTint="@color/primary_blue"
                    android:textColor="@android:color/white"
                    android:paddingStart="8dp"
                    android:paddingEnd="8dp"
                    android:paddingTop="2dp"
                    android:paddingBottom="2dp"
                    android:layout_marginEnd="8dp" />

                <TextView
                    android:id="@+id/command_action"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="forward"
                    android:textSize="14sp"
                    android:textColor="@color/text_primary" />

            </LinearLayout>

            <!-- Duration and Parameter -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/command_duration"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="3000ms"
                    android:textSize="12sp"
                    android:textColor="@color/text_secondary"
                    android:background="@drawable/tag_background"
                    android:backgroundTint="@color/surface_light"
                    android:paddingStart="6dp"
                    android:paddingEnd="6dp"
                    android:paddingTop="2dp"
                    android:paddingBottom="2dp"
                    android:layout_marginEnd="8dp"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/command_parameter"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="parameter"
                    android:textSize="12sp"
                    android:textColor="@color/text_secondary"
                    android:background="@drawable/tag_background"
                    android:backgroundTint="@color/surface_light"
                    android:paddingStart="6dp"
                    android:paddingEnd="6dp"
                    android:paddingTop="2dp"
                    android:paddingBottom="2dp"
                    android:visibility="gone" />

            </LinearLayout>

        </LinearLayout>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <!-- Move Up -->
            <Button
                android:id="@+id/btn_move_up"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:background="@drawable/circle_shape"
                android:backgroundTint="@color/button_secondary"
                android:text="↑"
                android:textColor="@android:color/white"
                android:textSize="12sp"
                android:layout_marginEnd="4dp"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:padding="0dp" />

            <!-- Move Down -->
            <Button
                android:id="@+id/btn_move_down"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:background="@drawable/circle_shape"
                android:backgroundTint="@color/button_secondary"
                android:text="↓"
                android:textColor="@android:color/white"
                android:textSize="12sp"
                android:layout_marginEnd="4dp"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:padding="0dp" />

            <!-- Edit -->
            <Button
                android:id="@+id/btn_edit_command"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:background="@drawable/circle_shape"
                android:backgroundTint="@color/button_primary"
                android:text="✏"
                android:textColor="@android:color/white"
                android:textSize="12sp"
                android:layout_marginEnd="4dp"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:padding="0dp" />

            <!-- Delete -->
            <Button
                android:id="@+id/btn_delete_command"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:background="@drawable/circle_shape"
                android:backgroundTint="@color/button_danger"
                android:text="✕"
                android:textColor="@android:color/white"
                android:textSize="12sp"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:padding="0dp" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
