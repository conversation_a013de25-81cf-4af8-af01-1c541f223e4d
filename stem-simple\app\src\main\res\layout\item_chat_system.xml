<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="8dp"
    android:gravity="center">

    <com.google.android.material.card.MaterialCardView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        app:cardBackgroundColor="@color/accent_orange"
        app:cardCornerRadius="12dp"
        app:cardElevation="1dp">

        <TextView
            android:id="@+id/message_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="8dp"
            android:text="System message"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:textStyle="italic" />

    </com.google.android.material.card.MaterialCardView>

    <TextView
        android:id="@+id/time_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="2dp"
        android:text="12:34"
        android:textColor="@color/text_secondary"
        android:textSize="10sp" />

</LinearLayout>
