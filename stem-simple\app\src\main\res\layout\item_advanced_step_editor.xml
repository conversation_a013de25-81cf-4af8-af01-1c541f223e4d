<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Step Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <!-- Step Number -->
            <TextView
                android:id="@+id/step_number"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:background="@drawable/circle_shape"
                android:backgroundTint="@color/primary_blue"
                android:text="1"
                android:textColor="@android:color/white"
                android:textSize="14sp"
                android:textStyle="bold"
                android:gravity="center"
                android:layout_marginEnd="12dp" />

            <!-- Step Info -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/step_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Step Name"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary" />

                <TextView
                    android:id="@+id/step_timing"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="0.0s - 2.0s (2.0s)"
                    android:textSize="12sp"
                    android:textColor="@color/text_secondary" />

                <TextView
                    android:id="@+id/step_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Step description"
                    android:textSize="12sp"
                    android:textColor="@color/text_secondary"
                    android:visibility="gone" />

            </LinearLayout>

            <!-- Enable/Disable Switch -->
            <Switch
                android:id="@+id/step_enabled"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp" />

            <!-- Toggle Details Button -->
            <Button
                android:id="@+id/btn_toggle_details"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:background="@drawable/circle_shape"
                android:backgroundTint="@color/button_secondary"
                android:text="▶"
                android:textColor="@android:color/white"
                android:textSize="12sp"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:padding="0dp" />

        </LinearLayout>

        <!-- Actions Summary -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Actions: "
                android:textSize="12sp"
                android:textColor="@color/text_secondary" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/actions_recycler_view"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:nestedScrollingEnabled="false" />

        </LinearLayout>

        <!-- Expanded Details -->
        <LinearLayout
            android:id="@+id/expanded_details"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone">

            <!-- Actions Container -->
            <LinearLayout
                android:id="@+id/actions_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginBottom="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Actions in this step:"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="8dp" />

                <!-- Add Action Button -->
                <Button
                    android:id="@+id/btn_add_action"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="+ Add Action"
                    android:background="@color/button_success"
                    android:textColor="@android:color/white"
                    android:layout_marginBottom="8dp" />

            </LinearLayout>

            <!-- Step Control Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center">

                <!-- Move Up -->
                <Button
                    android:id="@+id/btn_move_up"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="4dp"
                    android:text="↑"
                    android:background="@color/button_secondary"
                    android:textColor="@android:color/white" />

                <!-- Move Down -->
                <Button
                    android:id="@+id/btn_move_down"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="4dp"
                    android:layout_marginEnd="4dp"
                    android:text="↓"
                    android:background="@color/button_secondary"
                    android:textColor="@android:color/white" />

                <!-- Edit -->
                <Button
                    android:id="@+id/btn_edit_step"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="4dp"
                    android:layout_marginEnd="4dp"
                    android:text="Edit"
                    android:background="@color/button_primary"
                    android:textColor="@android:color/white" />

                <!-- Duplicate -->
                <Button
                    android:id="@+id/btn_duplicate_step"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="4dp"
                    android:layout_marginEnd="4dp"
                    android:text="Copy"
                    android:background="@color/button_primary"
                    android:textColor="@android:color/white" />

                <!-- Delete -->
                <Button
                    android:id="@+id/btn_delete_step"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="4dp"
                    android:text="Delete"
                    android:background="@color/button_danger"
                    android:textColor="@android:color/white" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
