package com.stemrobo.simple.utils;

import android.app.ProgressDialog;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.View;
import android.widget.ProgressBar;
import android.widget.TextView;

/**
 * Loading Manager for STEM Simple Robot
 * Handles loading indicators and progress feedback
 */
public class LoadingManager {
    private static final String TAG = "LoadingManager";
    
    private final Context context;
    private ProgressDialog progressDialog;
    private final Handler mainHandler;
    
    public LoadingManager(Context context) {
        this.context = context;
        this.mainHandler = new Handler(Looper.getMainLooper());
    }
    
    /**
     * Show loading dialog with message
     */
    public void showLoading(String message) {
        showLoading(message, false);
    }
    
    /**
     * Show loading dialog with message and cancellable option
     */
    public void showLoading(String message, boolean cancellable) {
        mainHandler.post(() -> {
            try {
                hideLoading(); // Hide any existing dialog
                
                progressDialog = new ProgressDialog(context);
                progressDialog.setMessage(message != null ? message : "Loading...");
                progressDialog.setCancelable(cancellable);
                progressDialog.setIndeterminate(true);
                progressDialog.show();
                
                Log.d(TAG, "Loading dialog shown: " + message);
            } catch (Exception e) {
                Log.e(TAG, "Error showing loading dialog", e);
            }
        });
    }
    
    /**
     * Hide loading dialog
     */
    public void hideLoading() {
        mainHandler.post(() -> {
            try {
                if (progressDialog != null && progressDialog.isShowing()) {
                    progressDialog.dismiss();
                    progressDialog = null;
                    Log.d(TAG, "Loading dialog hidden");
                }
            } catch (Exception e) {
                Log.e(TAG, "Error hiding loading dialog", e);
            }
        });
    }
    
    /**
     * Update loading message
     */
    public void updateLoadingMessage(String message) {
        mainHandler.post(() -> {
            try {
                if (progressDialog != null && progressDialog.isShowing()) {
                    progressDialog.setMessage(message != null ? message : "Loading...");
                }
            } catch (Exception e) {
                Log.e(TAG, "Error updating loading message", e);
            }
        });
    }
    
    /**
     * Show progress bar in view
     */
    public void showProgressBar(ProgressBar progressBar) {
        if (progressBar != null) {
            mainHandler.post(() -> {
                try {
                    progressBar.setVisibility(View.VISIBLE);
                    progressBar.setIndeterminate(true);
                } catch (Exception e) {
                    Log.e(TAG, "Error showing progress bar", e);
                }
            });
        }
    }
    
    /**
     * Hide progress bar in view
     */
    public void hideProgressBar(ProgressBar progressBar) {
        if (progressBar != null) {
            mainHandler.post(() -> {
                try {
                    progressBar.setVisibility(View.GONE);
                } catch (Exception e) {
                    Log.e(TAG, "Error hiding progress bar", e);
                }
            });
        }
    }
    
    /**
     * Update status text with loading animation
     */
    public void updateStatusWithAnimation(TextView statusText, String message) {
        if (statusText != null && message != null) {
            mainHandler.post(() -> {
                try {
                    statusText.setText(message);
                    
                    // Add simple fade animation
                    statusText.setAlpha(0.5f);
                    statusText.animate()
                        .alpha(1.0f)
                        .setDuration(300)
                        .start();
                        
                } catch (Exception e) {
                    Log.e(TAG, "Error updating status with animation", e);
                }
            });
        }
    }
    
    /**
     * Show temporary loading with auto-hide
     */
    public void showTemporaryLoading(String message, long durationMs) {
        showLoading(message, false);
        
        mainHandler.postDelayed(() -> {
            hideLoading();
        }, durationMs);
    }
    
    /**
     * Check if loading is currently shown
     */
    public boolean isLoading() {
        return progressDialog != null && progressDialog.isShowing();
    }
    
    /**
     * Cleanup resources
     */
    public void cleanup() {
        mainHandler.post(() -> {
            try {
                hideLoading();
                Log.d(TAG, "LoadingManager cleaned up");
            } catch (Exception e) {
                Log.e(TAG, "Error during cleanup", e);
            }
        });
    }
    
    /**
     * Show loading with timeout
     */
    public void showLoadingWithTimeout(String message, long timeoutMs, Runnable onTimeout) {
        showLoading(message, false);
        
        mainHandler.postDelayed(() -> {
            if (isLoading()) {
                hideLoading();
                if (onTimeout != null) {
                    try {
                        onTimeout.run();
                    } catch (Exception e) {
                        Log.e(TAG, "Error in timeout callback", e);
                    }
                }
            }
        }, timeoutMs);
    }
    
    /**
     * Create simple loading overlay for views
     */
    public static void setViewLoading(View view, boolean loading) {
        if (view != null) {
            try {
                view.setEnabled(!loading);
                view.setAlpha(loading ? 0.5f : 1.0f);
            } catch (Exception e) {
                Log.e(TAG, "Error setting view loading state", e);
            }
        }
    }
}
