package com.stemrobo.simple.ui;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;
import com.google.android.material.switchmaterial.SwitchMaterial;
import com.stemrobo.simple.utils.LanguageManager;
import android.widget.ArrayAdapter;
import android.widget.AdapterView;
import android.util.Log;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.stemrobo.simple.MainActivity;
import com.stemrobo.simple.R;
import com.stemrobo.simple.ai.*;
import com.stemrobo.simple.ai.GeminiAIService;
import com.stemrobo.simple.ai.CommandParser;
import com.stemrobo.simple.robot.SimpleRobotController;
import com.stemrobo.simple.services.VoiceService;
import com.stemrobo.simple.services.TTSService;
import com.stemrobo.simple.utils.LanguageManager;
import com.stemrobo.simple.activities.LMSVideoPlayerActivity;
import com.stemrobo.simple.activities.LMSYouTubePlayerActivity;
import com.stemrobo.simple.utils.VideoStopManager;
import java.util.ArrayList;
import java.util.List;

/**
 * AI Fragment for unified AI interface
 * Features:
 * - Voice input with wake word detection
 * - Text input for commands
 * - Structured AI responses with command execution

 * - Chat history display
 */
public class AIFragment extends Fragment {
    private static final String TAG = "AIFragment";
    
    // UI Components
    private RecyclerView chatRecyclerView;
    private EditText commandInput;
    private Button btnVoiceInput;
    private Button btnSendText;
    private Button btnClearChat;
    private Button btnEmergencyStop;
    private TextView aiStatus;
    private View listeningIndicator;
    private SwitchMaterial switchAutoVoice;
    private Spinner spinnerAILanguage;
    
    // AI System Components
    private GeminiAIService aiService;
    private CommandParser commandParser;
    private VoiceService voiceService;
    private TTSService ttsService;
    private SimpleRobotController robotController;
    private LanguageManager languageManager;
    
    // UI Adapters
    private ChatAdapter chatAdapter;
    private List<ChatMessage> chatMessages;
    
    // State
    private boolean isListening = false;
    private boolean isProcessing = false;
    
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_ai, container, false);
        
        initializeViews(view);
        setupRecyclerViews();
        setupButtonListeners();
        
        return view;
    }
    
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        initializeAISystem();
        updateStatus("AI system ready");
    }
    
    private void initializeViews(View view) {
        chatRecyclerView = view.findViewById(R.id.chat_recycler_view);
        commandInput = view.findViewById(R.id.command_input);
        btnVoiceInput = view.findViewById(R.id.btn_voice_input);
        btnSendText = view.findViewById(R.id.btn_send_text);
        btnClearChat = view.findViewById(R.id.btn_clear_chat);
        btnEmergencyStop = view.findViewById(R.id.btn_emergency_stop);
        aiStatus = view.findViewById(R.id.ai_status);
        listeningIndicator = view.findViewById(R.id.listening_indicator);
        switchAutoVoice = view.findViewById(R.id.switch_auto_voice);
        spinnerAILanguage = view.findViewById(R.id.spinner_ai_language);
        
        // Initialize data lists
        chatMessages = new ArrayList<>();
    }
    
    private void setupRecyclerViews() {
        // Chat recycler view
        chatAdapter = new ChatAdapter(chatMessages);
        chatRecyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        chatRecyclerView.setAdapter(chatAdapter);

        // Add welcome message
        addChatMessage(new ChatMessage("Welcome! I'm your AI assistant. You can speak commands or type them.", ChatMessage.Type.SYSTEM));
    }
    
    private void setupButtonListeners() {
        btnVoiceInput.setOnClickListener(v -> toggleVoiceInput());
        btnSendText.setOnClickListener(v -> sendTextCommand());
        btnClearChat.setOnClickListener(v -> clearChat());
        btnEmergencyStop.setOnClickListener(v -> emergencyStop());

        // Auto voice recognition toggle
        switchAutoVoice.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (voiceService != null) {
                voiceService.setAutoVoiceEnabled(isChecked);
                updateStatus(isChecked ? "Auto voice recognition enabled" : "Auto voice recognition disabled");
            }
        });

        // Setup language spinner
        setupLanguageSpinner();
        
        // Enter key listener for text input
        commandInput.setOnEditorActionListener((v, actionId, event) -> {
            sendTextCommand();
            return true;
        });
    }
    
    private void initializeAISystem() {
        try {
            // Initialize language manager
            languageManager = new LanguageManager(getContext());

            // Initialize Gemini AI service
            aiService = new GeminiAIService(getContext());
            
            // Get robot controller and smart greeting from MainActivity
            MainActivity mainActivity = (MainActivity) getActivity();
            if (mainActivity != null) {
                // Initialize command parser
                commandParser = new CommandParser(
                    mainActivity.getRobotController(),
                    mainActivity.getSmartGreeting()
                );
                
                commandParser.setExecutionCallback(new CommandParser.CommandExecutionCallback() {
                    @Override
                    public void onCommandStarted(AICommand command) {
                        updateStatus("Executing: " + command.getAction());
                    }
                    
                    @Override
                    public void onCommandCompleted(AICommand command) {
                        updateStatus("Completed: " + command.getAction());
                    }
                    
                    @Override
                    public void onCommandError(AICommand command, String error) {
                        updateStatus("Error: " + error);
                        showError("Command failed: " + error);
                    }
                    
                    @Override
                    public void onAllCommandsCompleted() {
                        updateStatus("All commands completed");
                    }
                });
                
            }
            
            // Initialize voice service
            voiceService = new VoiceService(getContext());
            voiceService.setVoiceCallback(new VoiceService.VoiceCallback() {
                @Override
                public void onVoiceResult(String text) {
                    if (getActivity() != null) {
                        getActivity().runOnUiThread(() -> {
                            if (!text.isEmpty()) {
                                // Translate robot commands to English for processing
                                String translatedText = languageManager.translateCommandToEnglish(text);
                                Log.d(TAG, "Voice input: " + text + " -> Translated: " + translatedText);
                                processUserInput(translatedText);
                            } else {
                                updateStatus("Wake word detected, listening for command...");
                                // Continue listening for actual command
                                voiceService.startListening();
                            }
                        });
                    }
                }
                
                @Override
                public void onVoiceError(String error) {
                    if (getActivity() != null) {
                        getActivity().runOnUiThread(() -> {
                            updateStatus("Voice error: " + error);
                            setListeningState(false);
                        });
                    }
                }
                
                @Override
                public void onListeningStarted() {
                    if (getActivity() != null) {
                        getActivity().runOnUiThread(() -> {
                            setListeningState(true);
                            updateStatus("Listening...");
                        });
                    }
                }
                
                @Override
                public void onListeningStopped() {
                    if (getActivity() != null) {
                        getActivity().runOnUiThread(() -> {
                            setListeningState(false);
                            updateStatus("Ready");
                        });
                    }
                }
            });
            
            // Initialize TTS service
            ttsService = new TTSService(getContext());
            ttsService.setTTSCallback(new TTSService.TTSCallback() {
                @Override
                public void onSpeechStarted(String text) {
                    updateStatus("Speaking...");
                }

                @Override
                public void onSpeechCompleted(String text) {
                    updateStatus("Ready");
                }

                @Override
                public void onSpeechError(String error) {
                    updateStatus("Speech error: " + error);
                }
            });

            // Connect TTS and Voice services for feedback prevention
            if (voiceService != null) {
                ttsService.setVoiceService(voiceService);
            }

            // Start continuous listening if auto voice is enabled (with delay to ensure initialization)
            new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() -> {
                if (switchAutoVoice.isChecked() && voiceService != null) {
                    voiceService.setAutoVoiceEnabled(true);
                    updateStatus("Auto voice recognition started");
                }
            }, 1000); // 1 second delay
            


            // Set up Gemini AI action callback
            MainActivity activity = (MainActivity) getActivity();
            if (activity != null) {
                robotController = activity.getRobotController();

                aiService.setActionCallback(new GeminiAIService.ActionExecutionCallback() {
                    @Override
                    public void executeMovement(String direction, int duration) {
                        if (robotController != null) {
                            switch (direction.toLowerCase()) {
                                case "forward":
                                    robotController.moveForward();
                                    break;
                                case "backward":
                                    robotController.moveBackward();
                                    break;
                                case "left":
                                    robotController.turnLeft();
                                    break;
                                case "right":
                                    robotController.turnRight();
                                    break;
                                case "stop":
                                    robotController.stopMovement();
                                    break;
                            }
                        }
                    }

                    @Override
                    public void executeServo(String servo, int position) {
                        if (robotController != null) {
                            switch (servo.toLowerCase()) {
                                case "left_arm":
                                    robotController.setLeftArmPosition(position);
                                    break;
                                case "right_arm":
                                    robotController.setRightArmPosition(position);
                                    break;
                                case "head":
                                    robotController.setHeadPosition(position);
                                    break;
                            }
                        }
                    }

                    @Override
                    public void executeGesture(String gesture) {
                        if (robotController != null) {
                            switch (gesture.toLowerCase()) {
                                case "wave":
                                    robotController.performWave();
                                    break;
                                case "point":
                                    robotController.performPoint();
                                    break;
                                case "handshake":
                                    robotController.performHandshake();
                                    break;
                                case "rest":
                                    robotController.moveToRestPosition();
                                    break;
                            }
                        }
                    }

                    @Override
                    public void executeHeadMovement(String direction) {
                        if (robotController != null) {
                            switch (direction.toLowerCase()) {
                                case "left":
                                    robotController.lookLeft();
                                    break;
                                case "center":
                                    robotController.lookCenter();
                                    break;
                                case "right":
                                    robotController.lookRight();
                                    break;
                            }
                        }
                    }

                    @Override
                    public void executeLMSAction(String action) {
                        switch (action.toLowerCase()) {
                            case "intro":
                                launchLMSIntroVideo();
                                break;
                            case "class":
                                launchLMSClassVideo(1); // Default to class 1
                                break;
                        }
                    }

                    @Override
                    public void executeLMSAction(String action, int classNumber) {
                        switch (action.toLowerCase()) {
                            case "intro":
                                launchLMSIntroVideo();
                                break;
                            case "class":
                                launchLMSClassVideo(classNumber);
                                break;
                        }
                    }

                    @Override
                    public void executePreset(String presetName) {
                        // Preset functionality moved to dedicated Preset tab
                        addChatMessage(new ChatMessage("Please use the Presets tab to manage and execute presets", ChatMessage.Type.SYSTEM));
                    }

                    @Override
                    public void speakText(String text) {
                        if (ttsService != null) {
                            ttsService.speak(text);
                        }
                    }
                });
            }

        } catch (Exception e) {
            updateStatus("AI system initialization failed: " + e.getMessage());
            showError("Failed to initialize AI system: " + e.getMessage());
        }
    }
    
    private void toggleVoiceInput() {
        if (isListening) {
            voiceService.stopListening();
        } else {
            voiceService.startListening();
        }
    }
    
    private void sendTextCommand() {
        String command = commandInput.getText().toString().trim();
        if (!command.isEmpty()) {
            commandInput.setText("");
            // Translate robot commands to English for processing
            String translatedCommand = languageManager.translateCommandToEnglish(command);
            Log.d(TAG, "Text input: " + command + " -> Translated: " + translatedCommand);
            processUserInput(translatedCommand);
        }
    }
    
    private void processUserInput(String input) {
        if (isProcessing) {
            showMessage("Please wait, processing previous command...");
            return;
        }
        
        isProcessing = true;
        updateStatus("Processing...");
        
        // Add user message to chat
        addChatMessage(new ChatMessage(input, ChatMessage.Type.USER));
        
        // Process with Gemini AI service
        aiService.setResponseCallback(new GeminiAIService.AIResponseCallback() {
            @Override
            public void onResponse(String response, List<GeminiAIService.RobotAction> actions) {
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        handleGeminiResponse(response, actions);
                        isProcessing = false;
                    });
                }
            }

            @Override
            public void onError(String error) {
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        // Try fallback response
                        addChatMessage(new ChatMessage("Sorry, I'm having trouble processing that request. Please try again.", ChatMessage.Type.AI));
                        isProcessing = false;
                    });
                }
            }
        });

        aiService.processInput(input);
    }

    private void handleGeminiResponse(String response, List<GeminiAIService.RobotAction> actions) {
        // Add AI response to chat
        addChatMessage(new ChatMessage(response, ChatMessage.Type.AI));

        // Update status with action summary
        if (!actions.isEmpty()) {
            StringBuilder actionSummary = new StringBuilder("Executing: ");
            for (int i = 0; i < actions.size(); i++) {
                GeminiAIService.RobotAction action = actions.get(i);
                if (i > 0) actionSummary.append(", ");

                switch (action.type) {
                    case "movement":
                        actionSummary.append(action.action).append(" (").append(action.value).append("s)");
                        break;
                    case "servo":
                        actionSummary.append(action.action).append(" (").append(action.value).append("°)");
                        break;
                    case "gesture":
                        actionSummary.append(action.action);
                        break;
                    case "preset":
                        actionSummary.append("preset ").append(action.action);
                        break;
                    case "speak":
                        actionSummary.append("speaking");
                        break;
                }
            }
            updateStatus(actionSummary.toString());
        } else {
            updateStatus("Response ready");
        }
    }
    
    private void handleAIResponse(AIResponse response) {
        // Add AI response to chat
        addChatMessage(new ChatMessage(response.getResponse(), ChatMessage.Type.AI));
        
        // Speak the response
        if (ttsService != null && ttsService.isReady()) {
            ttsService.speak(response.getResponse());
        }
        
        // Execute commands if present
        if (response.hasCommands()) {
            commandParser.executeCommands(response.getCommands());
        }
        
        // Preset creation moved to dedicated Preset tab
        
        updateStatus("Ready");
    }
    

    
    private void clearChat() {
        chatMessages.clear();
        chatAdapter.notifyDataSetChanged();
        addChatMessage(new ChatMessage("Chat cleared. How can I help you?", ChatMessage.Type.SYSTEM));
    }

    private void emergencyStop() {
        Log.d(TAG, "Emergency stop activated");

        // Stop all robot movements
        if (robotController != null) {
            robotController.stopAllMovements();
        }

        // Stop TTS
        if (ttsService != null) {
            ttsService.stopSpeaking();
        }

        // Stop any video playback
        VideoStopManager.getInstance().requestVideoStop();

        // Stop voice recognition and restart listening
        if (voiceService != null) {
            voiceService.stopListening();
            // Restart listening after a brief delay
            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                if (voiceService != null) {
                    voiceService.startListening();
                }
            }, 500);
        }

        // Update UI
        updateStatus("Emergency stop - All actions stopped");
        addChatMessage(new ChatMessage("🛑 Emergency stop activated - All actions stopped", ChatMessage.Type.SYSTEM));

        Log.d(TAG, "Emergency stop completed");
    }
    
    private void addChatMessage(ChatMessage message) {
        chatMessages.add(message);
        chatAdapter.notifyItemInserted(chatMessages.size() - 1);
        chatRecyclerView.scrollToPosition(chatMessages.size() - 1);
    }
    

    
    private void setListeningState(boolean listening) {
        isListening = listening;
        btnVoiceInput.setText(listening ? "🔴 Stop" : "🎤 Voice");
        btnVoiceInput.setBackgroundColor(getResources().getColor(
            listening ? R.color.button_danger : R.color.button_primary, null));

        // Update listening indicator
        if (listeningIndicator != null) {
            listeningIndicator.setBackgroundTintList(
                getResources().getColorStateList(
                    listening ? R.color.indicator_active : R.color.indicator_inactive, null));
        }
    }
    
    private void updateStatus(String status) {
        if (aiStatus != null) {
            aiStatus.setText(status);
        }
    }
    
    private void showMessage(String message) {
        if (getContext() != null) {
            Toast.makeText(getContext(), message, Toast.LENGTH_SHORT).show();
        }
    }
    
    private void showError(String error) {
        if (getContext() != null) {
            Toast.makeText(getContext(), "Error: " + error, Toast.LENGTH_LONG).show();
        }
    }
    
    private void setupLanguageSpinner() {
        try {
            LanguageManager languageManager = new LanguageManager(getContext());
            List<LanguageManager.Language> languages = LanguageManager.getSupportedLanguages();

            ArrayAdapter<LanguageManager.Language> adapter = new ArrayAdapter<>(
                getContext(),
                android.R.layout.simple_spinner_item,
                languages
            );
            adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
            spinnerAILanguage.setAdapter(adapter);

            // Set current language
            String currentLanguage = languageManager.getCurrentLanguage();
            for (int i = 0; i < languages.size(); i++) {
                if (languages.get(i).code.equals(currentLanguage)) {
                    spinnerAILanguage.setSelection(i);
                    break;
                }
            }

            // Set listener for language changes
            spinnerAILanguage.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                    LanguageManager.Language selectedLanguage = languages.get(position);
                    if (!selectedLanguage.code.equals(languageManager.getCurrentLanguage())) {
                        languageManager.setLanguage(selectedLanguage.code);
                        updateStatus("Language changed to " + selectedLanguage.nativeName);

                        // Update services with the new language
                        if (voiceService != null) {
                            voiceService.setLanguage(selectedLanguage.code);
                        }
                        if (ttsService != null) {
                            ttsService.setLanguage(selectedLanguage.code);
                        }
                        
                        // Add a system message to confirm the change
                        addChatMessage(new ChatMessage("Language updated to " + selectedLanguage.nativeName, ChatMessage.Type.SYSTEM));
                    }
                }

                @Override
                public void onNothingSelected(AdapterView<?> parent) {}
            });

        } catch (Exception e) {
            Log.e(TAG, "Error setting up language spinner", e);
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();

        // Cleanup services
        if (voiceService != null) {
            voiceService.cleanup();
        }
        if (ttsService != null) {
            ttsService.cleanup();
        }
        if (commandParser != null) {
            commandParser.cleanup();
        }

        if (aiService != null) {
            aiService.cleanup();
        }
    }

    // LMS Video Player Methods
    private void launchLMSIntroVideo() {
        try {
            Intent intent = new Intent(getContext(), LMSVideoPlayerActivity.class);
            intent.putExtra(LMSVideoPlayerActivity.EXTRA_VIDEO_PATH, "LMS-intro.mp4");
            intent.putExtra(LMSVideoPlayerActivity.EXTRA_VIDEO_TITLE, "STEM-Xpert LMS Introduction");
            startActivity(intent);

            Log.d(TAG, "Launched LMS introduction video");
        } catch (Exception e) {
            Log.e(TAG, "Error launching LMS introduction video", e);
            updateStatus("Error launching LMS video");
        }
    }

    private void launchLMSClassVideo(int classNumber) {
        try {
            Intent intent = new Intent(getContext(), LMSYouTubePlayerActivity.class);
            intent.putExtra(LMSYouTubePlayerActivity.EXTRA_CLASS_NUMBER, classNumber);
            startActivity(intent);

            Log.d(TAG, "Launched LMS class " + classNumber + " YouTube video");
        } catch (Exception e) {
            Log.e(TAG, "Error launching LMS class video", e);
            updateStatus("Error launching LMS class video");
        }
    }
}
