<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.STEMSimpleRobot" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/primary_blue</item>
        <item name="colorPrimaryVariant">@color/primary_blue_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/accent_orange</item>
        <item name="colorSecondaryVariant">@color/accent_orange_dark</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <!-- Customize your theme here. -->
        <item name="android:windowBackground">@color/background_light</item>
    </style>
    
    <!-- Button styles -->
    <style name="ButtonPrimary" parent="Widget.MaterialComponents.Button">
        <item name="backgroundTint">@color/button_primary</item>
        <item name="android:textColor">@color/white</item>
        <item name="cornerRadius">8dp</item>
    </style>
    
    <style name="ButtonSecondary" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="strokeColor">@color/button_secondary</item>
        <item name="android:textColor">@color/button_secondary</item>
        <item name="cornerRadius">8dp</item>
    </style>
    
    <style name="ButtonDanger" parent="Widget.MaterialComponents.Button">
        <item name="backgroundTint">@color/button_danger</item>
        <item name="android:textColor">@color/white</item>
        <item name="cornerRadius">8dp</item>
    </style>
    
    <style name="ButtonSuccess" parent="Widget.MaterialComponents.Button">
        <item name="backgroundTint">@color/button_success</item>
        <item name="android:textColor">@color/white</item>
        <item name="cornerRadius">8dp</item>
    </style>
</resources>
