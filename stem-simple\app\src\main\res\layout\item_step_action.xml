<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:padding="8dp"
    android:background="@drawable/action_item_background">

    <!-- Action Type Badge -->
    <TextView
        android:id="@+id/action_type"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="movement"
        android:textSize="10sp"
        android:textStyle="bold"
        android:background="@drawable/tag_background"
        android:backgroundTint="@color/primary_blue"
        android:textColor="@android:color/white"
        android:paddingStart="6dp"
        android:paddingEnd="6dp"
        android:paddingTop="2dp"
        android:paddingBottom="2dp"
        android:layout_marginEnd="8dp" />

    <!-- Action Details -->
    <TextView
        android:id="@+id/action_details"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="forward (3000ms)"
        android:textSize="12sp"
        android:textColor="@color/text_primary" />

    <!-- Action Buttons -->
    <Button
        android:id="@+id/btn_edit_action"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:background="@drawable/circle_shape"
        android:backgroundTint="@color/button_primary"
        android:text="✏"
        android:textColor="@android:color/white"
        android:textSize="10sp"
        android:layout_marginEnd="4dp"
        android:minWidth="0dp"
        android:minHeight="0dp"
        android:padding="0dp" />

    <Button
        android:id="@+id/btn_delete_action"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:background="@drawable/circle_shape"
        android:backgroundTint="@color/button_danger"
        android:text="✕"
        android:textColor="@android:color/white"
        android:textSize="10sp"
        android:minWidth="0dp"
        android:minHeight="0dp"
        android:padding="0dp" />

</LinearLayout>
