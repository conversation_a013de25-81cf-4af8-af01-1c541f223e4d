package com.stemrobo.simple.utils;

import android.content.Context;
import android.view.View;
import android.view.accessibility.AccessibilityEvent;
import android.view.accessibility.AccessibilityManager;
import android.widget.Button;
import android.widget.TextView;
import android.util.Log;

/**
 * Accessibility Helper for STEM Simple Robot
 * Provides accessibility features and announcements
 */
public class AccessibilityHelper {
    private static final String TAG = "AccessibilityHelper";
    
    private final Context context;
    private final AccessibilityManager accessibilityManager;
    
    public AccessibilityHelper(Context context) {
        this.context = context;
        this.accessibilityManager = (AccessibilityManager) context.getSystemService(Context.ACCESSIBILITY_SERVICE);
    }
    
    /**
     * Check if accessibility services are enabled
     */
    public boolean isAccessibilityEnabled() {
        return accessibilityManager != null && accessibilityManager.isEnabled();
    }
    
    /**
     * Announce text for screen readers
     */
    public void announceText(String text) {
        if (!isAccessibilityEnabled() || text == null) {
            return;
        }
        
        try {
            AccessibilityEvent event = AccessibilityEvent.obtain(AccessibilityEvent.TYPE_ANNOUNCEMENT);
            event.getText().add(text);
            accessibilityManager.sendAccessibilityEvent(event);
            Log.d(TAG, "Announced: " + text);
        } catch (Exception e) {
            Log.e(TAG, "Error announcing text", e);
        }
    }
    
    /**
     * Set content description for view
     */
    public void setContentDescription(View view, String description) {
        if (view != null && description != null) {
            try {
                view.setContentDescription(description);
            } catch (Exception e) {
                Log.e(TAG, "Error setting content description", e);
            }
        }
    }
    
    /**
     * Setup accessibility for robot control buttons
     */
    public void setupRobotControlAccessibility(Button forward, Button backward, Button left, Button right, Button stop) {
        setContentDescription(forward, "Move robot forward");
        setContentDescription(backward, "Move robot backward");
        setContentDescription(left, "Turn robot left");
        setContentDescription(right, "Turn robot right");
        setContentDescription(stop, "Stop robot movement");
        
        // Add click announcements
        if (forward != null) {
            forward.setOnClickListener(v -> announceText("Robot moving forward"));
        }
        if (backward != null) {
            backward.setOnClickListener(v -> announceText("Robot moving backward"));
        }
        if (left != null) {
            left.setOnClickListener(v -> announceText("Robot turning left"));
        }
        if (right != null) {
            right.setOnClickListener(v -> announceText("Robot turning right"));
        }
        if (stop != null) {
            stop.setOnClickListener(v -> announceText("Robot stopped"));
        }
    }
    
    /**
     * Setup accessibility for servo control buttons
     */
    public void setupServoControlAccessibility(Button wave, Button point, Button rest) {
        setContentDescription(wave, "Make robot wave");
        setContentDescription(point, "Make robot point");
        setContentDescription(rest, "Move robot to rest position");
        
        // Add click announcements
        if (wave != null) {
            wave.setOnClickListener(v -> announceText("Robot waving"));
        }
        if (point != null) {
            point.setOnClickListener(v -> announceText("Robot pointing"));
        }
        if (rest != null) {
            rest.setOnClickListener(v -> announceText("Robot moving to rest position"));
        }
    }
    
    /**
     * Announce robot status changes
     */
    public void announceRobotStatus(String status) {
        if (status != null) {
            announceText("Robot status: " + status);
        }
    }
    
    /**
     * Announce face detection results
     */
    public void announceFaceDetection(int faceCount) {
        if (faceCount == 0) {
            announceText("No faces detected");
        } else if (faceCount == 1) {
            announceText("One face detected");
        } else {
            announceText(faceCount + " faces detected");
        }
    }
    
    /**
     * Announce distance measurement
     */
    public void announceDistance(float distance) {
        if (distance >= 999) {
            announceText("Distance measurement unavailable");
        } else {
            announceText("Distance: " + String.format("%.1f", distance) + " centimeters");
        }
    }
    
    /**
     * Announce AI response
     */
    public void announceAIResponse(String response) {
        if (response != null && !response.trim().isEmpty()) {
            announceText("AI says: " + response);
        }
    }
    
    /**
     * Announce voice recognition status
     */
    public void announceVoiceStatus(boolean listening) {
        if (listening) {
            announceText("Listening for voice command");
        } else {
            announceText("Voice recognition stopped");
        }
    }
    
    /**
     * Setup accessibility for status displays
     */
    public void setupStatusDisplayAccessibility(TextView connectionStatus, TextView faceCount, TextView distance) {
        setContentDescription(connectionStatus, "Robot connection status");
        setContentDescription(faceCount, "Number of faces detected");
        setContentDescription(distance, "Distance measurement from ultrasonic sensor");
    }
    
    /**
     * Make view focusable for accessibility
     */
    public void makeFocusable(View view) {
        if (view != null) {
            try {
                view.setFocusable(true);
                view.setFocusableInTouchMode(true);
                view.setClickable(true);
            } catch (Exception e) {
                Log.e(TAG, "Error making view focusable", e);
            }
        }
    }
    
    /**
     * Setup accessibility for language selection
     */
    public void announceLanguageChange(String languageName) {
        if (languageName != null) {
            announceText("Language changed to " + languageName);
        }
    }
    
    /**
     * Announce preset execution
     */
    public void announcePresetExecution(String presetName) {
        if (presetName != null) {
            announceText("Executing preset: " + presetName);
        }
    }
    
    /**
     * Setup accessibility hints for complex controls
     */
    public void setupAccessibilityHints(View view, String hint) {
        if (view != null && hint != null) {
            try {
                view.setAccessibilityDelegate(new View.AccessibilityDelegate() {
                    @Override
                    public void onPopulateAccessibilityEvent(View host, AccessibilityEvent event) {
                        super.onPopulateAccessibilityEvent(host, event);
                        if (event.getEventType() == AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUSED) {
                            event.getText().add(hint);
                        }
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Error setting accessibility hint", e);
            }
        }
    }
}
