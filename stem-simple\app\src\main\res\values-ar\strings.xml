<resources>
    <string name="app_name">روبوت STEM البسيط</string>
    
    <!-- Tab titles -->
    <string name="tab_control">التحكم</string>
    <string name="tab_ai">مساعد الذكاء الاصطناعي</string>
    <string name="tab_settings">الإعدادات</string>
    
    <!-- Control tab -->
    <string name="movement_controls">عناصر التحكم في الحركة</string>
    <string name="servo_controls">عناصر التحكم في المحرك</string>
    <string name="sensor_status">حالة المستشعر</string>
    <string name="forward">للأمام</string>
    <string name="backward">للخلف</string>
    <string name="left">يسار</string>
    <string name="right">يمين</string>
    <string name="stop">توقف</string>
    <string name="wave">تلويح</string>
    <string name="point">إشارة</string>
    <string name="rest">راحة</string>
    <string name="distance">المسافة: %1$s سم</string>
    <string name="face_count">الوجوه: %1$d</string>
    
    <!-- AI tab -->
    <string name="ai_assistant">مساعد الذكاء الاصطناعي</string>
    <string name="speak_command">تحدث بأمرك</string>
    <string name="type_command">اكتب أمرك</string>
    <string name="send">إرسال</string>
    <string name="listening">الاستماع...</string>
    <string name="processing">المعالجة...</string>
    <string name="preset_created">تم إنشاء الإعداد المسبق: %1$s</string>
    <string name="preset_executed">تنفيذ الإعداد المسبق: %1$s</string>
    
    <!-- Settings tab -->
    <string name="robot_settings">إعدادات الروبوت</string>
    <string name="ai_settings">إعدادات الذكاء الاصطناعي</string>
    <string name="greeting_settings">إعدادات التحية الذكية</string>
    <string name="esp32_ip">عنوان IP الخاص بـ ESP32</string>
    <string name="greeting_enabled">تمكين التحية الذكية</string>
    <string name="greeting_distance">مسافة التحية (سم)</string>
    <string name="greeting_cooldown">فترة انتظار التحية (ثواني)</string>
    <string name="ai_wake_word">كلمة تنشيط الذكاء الاصطناعي</string>
    <string name="language">اللغة</string>
    <string name="voice_gender">جنس الصوت</string>
    <string name="male">ذكر</string>
    <string name="female">أنثى</string>
    
    <!-- Status messages -->
    <string name="robot_online">الروبوت متصل</string>
    <string name="robot_offline">الروبوت غير متصل</string>
    <string name="connecting">جاري الاتصال...</string>
    <string name="connection_failed">فشل الاتصال</string>
    <string name="camera_permission_required">مطلوب إذن الكاميرا</string>
    <string name="microphone_permission_required">مطلوب إذن الميكروفون</string>
    
    <!-- Error messages -->
    <string name="error_camera_init">فشل في تهيئة الكاميرا</string>
    <string name="error_ai_service">خدمة الذكاء الاصطناعي غير متاحة</string>
    <string name="error_robot_communication">خطأ في اتصال الروبوت</string>
    <string name="error_invalid_command">أمر غير صالح</string>
</resources>
