<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Movement Controls Section -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/movement_controls"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="16dp" />

                <!-- Movement buttons in cross pattern -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="vertical">

                    <!-- Forward button -->
                    <Button
                        android:id="@+id/btn_forward"
                        style="@style/ButtonPrimary"
                        android:layout_width="120dp"
                        android:layout_height="60dp"
                        android:text="@string/forward" />

                    <!-- Left, Stop, Right buttons -->
                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:orientation="horizontal">

                        <Button
                            android:id="@+id/btn_left"
                            style="@style/ButtonPrimary"
                            android:layout_width="120dp"
                            android:layout_height="60dp"
                            android:layout_marginEnd="8dp"
                            android:text="@string/left" />

                        <Button
                            android:id="@+id/btn_stop"
                            style="@style/ButtonDanger"
                            android:layout_width="120dp"
                            android:layout_height="60dp"
                            android:layout_marginHorizontal="8dp"
                            android:text="@string/stop" />

                        <Button
                            android:id="@+id/btn_right"
                            style="@style/ButtonPrimary"
                            android:layout_width="120dp"
                            android:layout_height="60dp"
                            android:layout_marginStart="8dp"
                            android:text="@string/right" />

                    </LinearLayout>

                    <!-- Backward button -->
                    <Button
                        android:id="@+id/btn_backward"
                        style="@style/ButtonPrimary"
                        android:layout_width="120dp"
                        android:layout_height="60dp"
                        android:layout_marginTop="8dp"
                        android:text="@string/backward" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Servo Controls Section -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/servo_controls"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="16dp" />

                <!-- Arm Servo Controls -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/arm_controls"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <!-- Left Arm Control -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:layout_width="80dp"
                        android:layout_height="wrap_content"
                        android:text="@string/left_arm"
                        android:textSize="14sp" />

                    <SeekBar
                        android:id="@+id/seekbar_left_arm"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:max="180"
                        android:progress="180"
                        android:layout_marginStart="8dp"
                        android:layout_marginEnd="8dp" />

                    <TextView
                        android:id="@+id/text_left_arm_value"
                        android:layout_width="40dp"
                        android:layout_height="wrap_content"
                        android:text="180°"
                        android:textSize="12sp"
                        android:gravity="center" />

                </LinearLayout>

                <!-- Right Arm Control -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:layout_width="80dp"
                        android:layout_height="wrap_content"
                        android:text="@string/right_arm"
                        android:textSize="14sp" />

                    <SeekBar
                        android:id="@+id/seekbar_right_arm"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:max="180"
                        android:progress="0"
                        android:layout_marginStart="8dp"
                        android:layout_marginEnd="8dp" />

                    <TextView
                        android:id="@+id/text_right_arm_value"
                        android:layout_width="40dp"
                        android:layout_height="wrap_content"
                        android:text="0°"
                        android:textSize="12sp"
                        android:gravity="center" />

                </LinearLayout>

                <!-- Head Servo Controls -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/head_controls"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:layout_marginTop="16dp"
                    android:layout_marginBottom="8dp" />

                <!-- Head Position Control -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="16dp">

                    <TextView
                        android:layout_width="80dp"
                        android:layout_height="wrap_content"
                        android:text="@string/head_position"
                        android:textSize="14sp" />

                    <SeekBar
                        android:id="@+id/seekbar_head"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:max="180"
                        android:progress="90"
                        android:layout_marginStart="8dp"
                        android:layout_marginEnd="8dp" />

                    <TextView
                        android:id="@+id/text_head_value"
                        android:layout_width="40dp"
                        android:layout_height="wrap_content"
                        android:text="90°"
                        android:textSize="12sp"
                        android:gravity="center" />

                </LinearLayout>

                <!-- Gesture Buttons -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/gestures"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center"
                    android:layout_marginBottom="8dp">

                    <Button
                        android:id="@+id/btn_wave"
                        style="@style/ButtonSecondary"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginEnd="4dp"
                        android:text="@string/wave" />

                    <Button
                        android:id="@+id/btn_point"
                        style="@style/ButtonSecondary"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginHorizontal="4dp"
                        android:text="@string/point" />

                    <Button
                        android:id="@+id/btn_handshake"
                        style="@style/ButtonSecondary"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginHorizontal="4dp"
                        android:text="@string/handshake" />

                    <Button
                        android:id="@+id/btn_rest"
                        style="@style/ButtonSecondary"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="4dp"
                        android:text="@string/rest" />

                </LinearLayout>

                <!-- Head Movement Buttons -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center">

                    <Button
                        android:id="@+id/btn_look_left"
                        style="@style/ButtonSecondary"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginEnd="4dp"
                        android:text="@string/look_left" />

                    <Button
                        android:id="@+id/btn_look_center"
                        style="@style/ButtonSecondary"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginHorizontal="4dp"
                        android:text="@string/look_center" />

                    <Button
                        android:id="@+id/btn_look_right"
                        style="@style/ButtonSecondary"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="4dp"
                        android:text="@string/look_right" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Sensor Status Section -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/sensor_status"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="16dp" />

                <!-- Sensor readings -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/distance_reading"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Distance: -- cm"
                        android:textSize="16sp" />

                    <TextView
                        android:id="@+id/face_count_reading"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Faces: 0"
                        android:textSize="16sp" />

                </LinearLayout>

                <!-- Manual distance check button -->
                <Button
                    android:id="@+id/btn_check_distance"
                    style="@style/ButtonSecondary"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="16dp"
                    android:text="Check Distance" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

</ScrollView>
