package com.stemrobo.simple.activities;

import android.os.Bundle;
import android.view.View;
import android.view.WindowManager;
import android.widget.ImageButton;
import android.widget.Toast;
import android.util.Log;

import androidx.appcompat.app.AppCompatActivity;

import com.google.android.exoplayer2.ExoPlayer;
import com.google.android.exoplayer2.MediaItem;
import com.google.android.exoplayer2.PlaybackException;
import com.google.android.exoplayer2.Player;
import com.google.android.exoplayer2.ui.StyledPlayerView;
import com.google.android.exoplayer2.upstream.DefaultDataSource;
import com.google.android.exoplayer2.upstream.AssetDataSource;

import com.stemrobo.simple.R;
import com.stemrobo.simple.utils.VideoStopManager;

/**
 * Activity for playing local LMS introduction video in fullscreen
 */
public class LMSVideoPlayerActivity extends AppCompatActivity {
    private static final String TAG = "LMSVideoPlayer";
    
    public static final String EXTRA_VIDEO_PATH = "video_path";
    public static final String EXTRA_VIDEO_TITLE = "video_title";
    
    private ExoPlayer exoPlayer;
    private StyledPlayerView playerView;
    private ImageButton closeButton;
    private boolean isVideoStopped = false;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // Enable fullscreen mode
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN);
        
        // Hide action bar
        if (getSupportActionBar() != null) {
            getSupportActionBar().hide();
        }
        
        setContentView(R.layout.activity_lms_video_player);
        
        initializeViews();
        setupPlayer();
        
        // Get video path from intent
        String videoPath = getIntent().getStringExtra(EXTRA_VIDEO_PATH);
        String videoTitle = getIntent().getStringExtra(EXTRA_VIDEO_TITLE);
        
        if (videoPath != null) {
            playVideo(videoPath);
        } else {
            // Default to LMS intro video
            playVideo("LMS-intro.mp4");
        }
        
        Log.d(TAG, "LMS Video Player started for: " + (videoTitle != null ? videoTitle : "LMS Introduction"));

        // Register with VideoStopManager for voice stop commands
        VideoStopManager.getInstance().setCurrentVideoCallback(() -> {
            Log.d(TAG, "Stop command received via voice");
            stopVideo();
            finish();
        });
    }
    
    private void initializeViews() {
        playerView = findViewById(R.id.player_view);
        closeButton = findViewById(R.id.close_button);
        
        // Setup close button
        closeButton.setOnClickListener(v -> {
            Log.d(TAG, "Close button clicked");
            stopVideo();
            finish();
        });
        
        // Hide system UI for immersive experience
        hideSystemUI();
    }
    
    private void setupPlayer() {
        // Create ExoPlayer instance
        exoPlayer = new ExoPlayer.Builder(this).build();
        
        // Bind player to view
        playerView.setPlayer(exoPlayer);
        
        // Setup player listener
        exoPlayer.addListener(new Player.Listener() {
            @Override
            public void onPlaybackStateChanged(int playbackState) {
                switch (playbackState) {
                    case Player.STATE_READY:
                        Log.d(TAG, "Video ready to play");
                        break;
                    case Player.STATE_ENDED:
                        Log.d(TAG, "Video playback ended");
                        finish(); // Close activity when video ends
                        break;
                    case Player.STATE_BUFFERING:
                        Log.d(TAG, "Video buffering");
                        break;
                }
            }
            
            @Override
            public void onPlayerError(PlaybackException error) {
                Log.e(TAG, "Video playback error: " + error.getMessage());
                Toast.makeText(LMSVideoPlayerActivity.this, 
                    "Error playing video", Toast.LENGTH_SHORT).show();
                finish();
            }
        });
        
        // Auto-play when ready
        exoPlayer.setPlayWhenReady(true);
    }
    
    private void playVideo(String videoPath) {
        try {
            // Create media item from asset
            MediaItem mediaItem = MediaItem.fromUri("asset:///" + videoPath);
            
            // Set media item and prepare
            exoPlayer.setMediaItem(mediaItem);
            exoPlayer.prepare();
            
            Log.d(TAG, "Playing video: " + videoPath);
            
        } catch (Exception e) {
            Log.e(TAG, "Error playing video: " + videoPath, e);
            Toast.makeText(this, "Error playing video", Toast.LENGTH_SHORT).show();
            finish();
        }
    }
    
    public void stopVideo() {
        if (exoPlayer != null && !isVideoStopped) {
            Log.d(TAG, "Stopping video playback");
            exoPlayer.stop();
            isVideoStopped = true;
        }
    }
    
    private void hideSystemUI() {
        View decorView = getWindow().getDecorView();
        decorView.setSystemUiVisibility(
            View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
            | View.SYSTEM_UI_FLAG_LAYOUT_STABLE
            | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
            | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
            | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
            | View.SYSTEM_UI_FLAG_FULLSCREEN);
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (exoPlayer != null) {
            exoPlayer.release();
            exoPlayer = null;
        }
        // Clear video stop callback
        VideoStopManager.getInstance().clearCurrentVideoCallback();
    }
    
    @Override
    protected void onPause() {
        super.onPause();
        if (exoPlayer != null) {
            exoPlayer.pause();
        }
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        if (exoPlayer != null && !isVideoStopped) {
            exoPlayer.play();
        }
        hideSystemUI();
    }
    
    @Override
    public void onBackPressed() {
        Log.d(TAG, "Back button pressed");
        stopVideo();
        super.onBackPressed();
    }
}
