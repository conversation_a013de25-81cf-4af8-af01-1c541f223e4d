package com.stemrobo.simple.ai;

import java.util.ArrayList;
import java.util.List;

/**
 * Represents a single step in a preset sequence that can contain multiple simultaneous actions.
 * Each step has a start time and duration, and can execute multiple actions in parallel.
 */
public class PresetStep {
    private long id;
    private String name;
    private String description;
    private int startTimeMs;
    private int durationMs;
    private List<AICommand> actions;
    private boolean enabled;
    
    public PresetStep() {
        this.id = System.currentTimeMillis();
        this.name = "";
        this.description = "";
        this.startTimeMs = 0;
        this.durationMs = 2000; // Default 2 seconds
        this.actions = new ArrayList<>();
        this.enabled = true;
    }
    
    public PresetStep(String name, int startTimeMs, int durationMs) {
        this();
        this.name = name;
        this.startTimeMs = startTimeMs;
        this.durationMs = durationMs;
    }
    
    // Getters
    public long getId() { return id; }
    public String getName() { return name; }
    public String getDescription() { return description; }
    public int getStartTimeMs() { return startTimeMs; }
    public int getDurationMs() { return durationMs; }
    public List<AICommand> getActions() { return actions; }
    public boolean isEnabled() { return enabled; }
    
    // Setters
    public void setId(long id) { this.id = id; }
    public void setName(String name) { this.name = name; }
    public void setDescription(String description) { this.description = description; }
    public void setStartTimeMs(int startTimeMs) { this.startTimeMs = startTimeMs; }
    public void setDurationMs(int durationMs) { this.durationMs = durationMs; }
    public void setActions(List<AICommand> actions) { this.actions = actions; }
    public void setEnabled(boolean enabled) { this.enabled = enabled; }
    
    // Utility methods
    public void addAction(AICommand action) {
        this.actions.add(action);
    }
    
    public void removeAction(int index) {
        if (index >= 0 && index < actions.size()) {
            this.actions.remove(index);
        }
    }
    
    public void insertAction(int index, AICommand action) {
        if (index >= 0 && index <= actions.size()) {
            this.actions.add(index, action);
        }
    }
    
    public int getActionCount() {
        return actions.size();
    }
    
    public int getEndTimeMs() {
        return startTimeMs + durationMs;
    }
    
    public float getStartTimeSeconds() {
        return startTimeMs / 1000.0f;
    }
    
    public float getDurationSeconds() {
        return durationMs / 1000.0f;
    }
    
    public float getEndTimeSeconds() {
        return getEndTimeMs() / 1000.0f;
    }
    
    public boolean hasActions() {
        return !actions.isEmpty();
    }
    
    public boolean overlaps(PresetStep other) {
        int thisStart = this.startTimeMs;
        int thisEnd = this.getEndTimeMs();
        int otherStart = other.startTimeMs;
        int otherEnd = other.getEndTimeMs();
        
        return thisStart < otherEnd && otherStart < thisEnd;
    }
    
    public PresetStep copy() {
        PresetStep copy = new PresetStep();
        copy.setName(this.name + " (Copy)");
        copy.setDescription(this.description);
        copy.setStartTimeMs(this.startTimeMs);
        copy.setDurationMs(this.durationMs);
        copy.setEnabled(this.enabled);
        
        // Deep copy actions
        for (AICommand action : this.actions) {
            AICommand actionCopy = new AICommand(action.getType(), action.getAction(), action.getDuration(), action.getParameter());
            copy.addAction(actionCopy);
        }
        
        return copy;
    }
    
    @Override
    public String toString() {
        return String.format("PresetStep{name='%s', start=%.1fs, duration=%.1fs, actions=%d}", 
                           name, getStartTimeSeconds(), getDurationSeconds(), getActionCount());
    }
    
    // Validation
    public boolean isValid() {
        return name != null && !name.trim().isEmpty() && 
               startTimeMs >= 0 && durationMs > 0;
    }
    
    public List<String> getValidationErrors() {
        List<String> errors = new ArrayList<>();
        
        if (name == null || name.trim().isEmpty()) {
            errors.add("Step name is required");
        }
        
        if (startTimeMs < 0) {
            errors.add("Start time cannot be negative");
        }
        
        if (durationMs <= 0) {
            errors.add("Duration must be greater than 0");
        }
        
        if (actions.isEmpty()) {
            errors.add("At least one action is required");
        }
        
        return errors;
    }
}
