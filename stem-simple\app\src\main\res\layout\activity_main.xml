<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity">

    <!-- Compact Status Bar -->
    <LinearLayout
        android:id="@+id/status_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/primary_blue"
        android:orientation="horizontal"
        android:padding="4dp"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/connection_status"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Robot"
            android:textColor="@color/white"
            android:textSize="10sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/face_count_display"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:text="👤0"
            android:textColor="@color/white"
            android:textSize="10sp" />

        <TextView
            android:id="@+id/distance_display"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="📏--"
            android:textColor="@color/white"
            android:textSize="10sp" />

    </LinearLayout>

    <!-- Camera Preview (Mini) -->
    <androidx.camera.view.PreviewView
        android:id="@+id/mini_camera_preview"
        android:layout_width="60dp"
        android:layout_height="45dp"
        android:layout_margin="4dp"
        android:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/status_bar" />

    <!-- Tab Layout -->
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tab_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/surface_light"
        app:layout_constraintTop_toBottomOf="@id/status_bar"
        app:tabGravity="fill"
        app:tabMode="fixed"
        app:tabSelectedTextColor="@color/primary_blue"
        app:tabTextColor="@color/text_secondary" />

    <!-- ViewPager2 for tab content -->
    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/view_pager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tab_layout" />

</androidx.constraintlayout.widget.ConstraintLayout>
