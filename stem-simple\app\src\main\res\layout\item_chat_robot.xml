<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="8dp"
    android:gravity="start">

    <!-- Robot Avatar -->
    <ImageView
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginEnd="8dp"
        android:background="@drawable/robot_avatar_background"
        android:src="@drawable/ic_robot"
        android:padding="8dp"
        android:layout_gravity="top" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical"
        android:layout_marginEnd="64dp">

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="start"
            app:cardBackgroundColor="@color/surface_light"
            app:cardCornerRadius="16dp"
            app:cardElevation="2dp">

            <TextView
                android:id="@+id/message_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="12dp"
                android:text="AI response"
                android:textColor="@color/text_primary"
                android:textSize="16sp" />

        </com.google.android.material.card.MaterialCardView>

        <TextView
            android:id="@+id/time_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="start"
            android:layout_marginTop="4dp"
            android:text="12:34"
            android:textColor="@color/text_secondary"
            android:textSize="12sp" />

    </LinearLayout>

</LinearLayout>
