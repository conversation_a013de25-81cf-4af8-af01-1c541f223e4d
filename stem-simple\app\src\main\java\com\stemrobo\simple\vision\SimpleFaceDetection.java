package com.stemrobo.simple.vision;

import android.content.Context;
import android.graphics.Rect;
import android.util.Log;
import android.util.Size;
import androidx.annotation.NonNull;
import androidx.camera.core.*;
import androidx.camera.lifecycle.ProcessCameraProvider;
import androidx.camera.view.PreviewView;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.LifecycleOwner;
import com.google.android.gms.tasks.OnFailureListener;
import com.google.android.gms.tasks.OnSuccessListener;
import com.google.mlkit.vision.common.InputImage;
import com.google.mlkit.vision.face.Face;
import com.google.mlkit.vision.face.FaceDetection;
import com.google.mlkit.vision.face.FaceDetector;
import com.google.mlkit.vision.face.FaceDetectorOptions;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Simplified Face Detection for STEM Simple Robot
 * Features:
 * - Real-time face detection using ML Kit
 * - Face count tracking
 * - Integration with smart greeting system
 * - Minimal camera preview for status monitoring
 */
public class SimpleFaceDetection {
    private static final String TAG = "SimpleFaceDetection";
    
    private final Context context;
    private final PreviewView previewView;
    private final ExecutorService cameraExecutor;
    
    // Camera components
    private ProcessCameraProvider cameraProvider;
    private Preview preview;
    private ImageAnalysis imageAnalysis;
    private Camera camera;
    
    // Face detection
    private FaceDetector faceDetector;
    private int currentFaceCount = 0;
    private long lastDetectionTime = 0;
    private static final long DETECTION_INTERVAL = 500; // 500ms between detections
    
    // Callbacks
    private FaceCountCallback faceCountCallback;
    private DistanceCallback distanceCallback;
    private SmartGreetingCallback smartGreetingCallback;
    
    public interface FaceCountCallback {
        void onFaceCountChanged(int count);
    }
    
    public interface DistanceCallback {
        void onDistanceUpdate(float distance);
    }
    
    public interface SmartGreetingCallback {
        void onGreetingTriggered(List<Face> faces);
    }
    
    public SimpleFaceDetection(Context context, PreviewView previewView) {
        this.context = context;
        this.previewView = previewView;
        this.cameraExecutor = Executors.newSingleThreadExecutor();
        
        initializeFaceDetector();
    }
    
    private void initializeFaceDetector() {
        // Configure face detector for real-time performance
        FaceDetectorOptions options = new FaceDetectorOptions.Builder()
            .setPerformanceMode(FaceDetectorOptions.PERFORMANCE_MODE_FAST)
            .setLandmarkMode(FaceDetectorOptions.LANDMARK_MODE_NONE)
            .setClassificationMode(FaceDetectorOptions.CLASSIFICATION_MODE_NONE)
            .setMinFaceSize(0.1f) // Minimum face size (10% of image)
            .enableTracking() // Enable face tracking for better performance
            .build();
        
        faceDetector = FaceDetection.getClient(options);
        Log.d(TAG, "Face detector initialized");
    }
    
    public void startFaceDetection() {
        ProcessCameraProvider.getInstance(context).addListener(() -> {
            try {
                cameraProvider = ProcessCameraProvider.getInstance(context).get();
                bindCameraUseCases();
                Log.d(TAG, "Face detection started");
            } catch (Exception e) {
                Log.e(TAG, "Failed to start face detection", e);
            }
        }, ContextCompat.getMainExecutor(context));
    }
    
    private void bindCameraUseCases() {
        if (cameraProvider == null) {
            Log.e(TAG, "Camera provider is null");
            return;
        }
        
        // Unbind all use cases before rebinding
        cameraProvider.unbindAll();
        
        // Create preview use case
        preview = new Preview.Builder()
            .setTargetResolution(new Size(640, 480)) // Lower resolution for better performance
            .build();
        
        // Connect preview to PreviewView
        preview.setSurfaceProvider(previewView.getSurfaceProvider());
        
        // Create image analysis use case for face detection
        imageAnalysis = new ImageAnalysis.Builder()
            .setTargetResolution(new Size(640, 480))
            .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
            .build();
        
        imageAnalysis.setAnalyzer(cameraExecutor, this::analyzeImage);
        
        // Select camera (front camera preferred for face detection)
        CameraSelector cameraSelector = CameraSelector.DEFAULT_FRONT_CAMERA;
        
        try {
            // Bind use cases to camera
            camera = cameraProvider.bindToLifecycle(
                (LifecycleOwner) context,
                cameraSelector,
                preview,
                imageAnalysis
            );
            
            Log.d(TAG, "Camera use cases bound successfully");
        } catch (Exception e) {
            Log.e(TAG, "Failed to bind camera use cases", e);
            
            // Try with back camera if front camera fails
            try {
                cameraSelector = CameraSelector.DEFAULT_BACK_CAMERA;
                camera = cameraProvider.bindToLifecycle(
                    (LifecycleOwner) context,
                    cameraSelector,
                    preview,
                    imageAnalysis
                );
                Log.d(TAG, "Camera use cases bound with back camera");
            } catch (Exception e2) {
                Log.e(TAG, "Failed to bind camera use cases with back camera", e2);
            }
        }
    }
    
    @androidx.camera.core.ExperimentalGetImage
    private void analyzeImage(@NonNull ImageProxy imageProxy) {
        // Throttle face detection to improve performance
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastDetectionTime < DETECTION_INTERVAL) {
            imageProxy.close();
            return;
        }
        lastDetectionTime = currentTime;
        
        // Convert ImageProxy to InputImage
        InputImage image = InputImage.fromMediaImage(
            imageProxy.getImage(),
            imageProxy.getImageInfo().getRotationDegrees()
        );
        
        // Process image for face detection
        faceDetector.process(image)
            .addOnSuccessListener(new OnSuccessListener<List<Face>>() {
                @Override
                public void onSuccess(List<Face> faces) {
                    processFaceDetectionResult(faces);
                    imageProxy.close();
                }
            })
            .addOnFailureListener(new OnFailureListener() {
                @Override
                public void onFailure(@NonNull Exception e) {
                    Log.e(TAG, "Face detection failed", e);
                    imageProxy.close();
                }
            });
    }
    
    private void processFaceDetectionResult(List<Face> faces) {
        int faceCount = faces.size();
        
        // Update face count if changed
        if (faceCount != currentFaceCount) {
            currentFaceCount = faceCount;
            if (faceCountCallback != null) {
                faceCountCallback.onFaceCountChanged(faceCount);
            }
            Log.d(TAG, "Face count updated: " + faceCount);
        }
        
        // Trigger smart greeting if faces detected
        if (!faces.isEmpty() && smartGreetingCallback != null) {
            smartGreetingCallback.onGreetingTriggered(faces);
        }
    }
    
    // ===== CALLBACK SETTERS =====
    
    public void setFaceCountCallback(FaceCountCallback callback) {
        this.faceCountCallback = callback;
    }
    
    public void setDistanceCallback(DistanceCallback callback) {
        this.distanceCallback = callback;
    }
    
    public void setSmartGreetingCallback(SmartGreetingCallback callback) {
        this.smartGreetingCallback = callback;
    }
    
    // ===== GETTERS =====
    
    public int getCurrentFaceCount() {
        return currentFaceCount;
    }
    
    public boolean isCameraActive() {
        return camera != null;
    }
    
    // ===== UTILITY METHODS =====
    
    /**
     * Get the largest face from the detected faces
     */
    public Face getLargestFace(List<Face> faces) {
        if (faces.isEmpty()) {
            return null;
        }
        
        Face largestFace = faces.get(0);
        float largestArea = getFaceArea(largestFace);
        
        for (Face face : faces) {
            float area = getFaceArea(face);
            if (area > largestArea) {
                largestFace = face;
                largestArea = area;
            }
        }
        
        return largestFace;
    }
    
    private float getFaceArea(Face face) {
        Rect bounds = face.getBoundingBox();
        return bounds.width() * bounds.height();
    }
    
    /**
     * Check if face is close enough for greeting (based on face size)
     */
    public boolean isFaceCloseEnough(Face face, float threshold) {
        Rect bounds = face.getBoundingBox();
        float faceWidth = bounds.width();
        // Consider face close if width is more than threshold pixels
        return faceWidth > threshold;
    }
    
    // ===== LIFECYCLE METHODS =====
    
    public void stopFaceDetection() {
        try {
            if (cameraProvider != null) {
                cameraProvider.unbindAll();
            }
            if (faceDetector != null) {
                faceDetector.close();
            }
            if (cameraExecutor != null && !cameraExecutor.isShutdown()) {
                cameraExecutor.shutdown();
            }
            Log.d(TAG, "Face detection stopped");
        } catch (Exception e) {
            Log.e(TAG, "Error stopping face detection", e);
        }
    }
    
    public void pauseFaceDetection() {
        if (cameraProvider != null) {
            cameraProvider.unbind(imageAnalysis);
            Log.d(TAG, "Face detection paused");
        }
    }
    
    public void resumeFaceDetection() {
        if (cameraProvider != null && camera != null) {
            try {
                cameraProvider.bindToLifecycle(
                    (LifecycleOwner) context,
                    camera.getCameraInfo().getCameraSelector(),
                    imageAnalysis
                );
                Log.d(TAG, "Face detection resumed");
            } catch (Exception e) {
                Log.e(TAG, "Failed to resume face detection", e);
            }
        }
    }
}
