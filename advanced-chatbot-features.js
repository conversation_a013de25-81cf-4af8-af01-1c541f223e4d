// Advanced Chatbot Features for STEM-Xpert Robot Workshop
// Students can add these features to enhance their chatbot

class AdvancedRobotChatbot extends RobotChatbot {
    constructor() {
        super();
        this.conversationHistory = [];
        this.robotCommands = new Map();
        this.speechSynthesis = window.speechSynthesis;
        this.currentVoice = null;
        this.isTextToSpeechEnabled = false;
        this.robotConnectionStatus = false;
        
        this.initializeAdvancedFeatures();
        this.setupRobotCommands();
        this.initializeTextToSpeech();
    }

    initializeAdvancedFeatures() {
        // Add advanced UI elements
        this.addAdvancedControls();
        this.addConversationExport();
        this.addThemeToggle();
        this.addRobotControlPanel();
    }

    addAdvancedControls() {
        const chatInput = document.querySelector('.chat-input');
        
        // Create advanced controls container
        const advancedControls = document.createElement('div');
        advancedControls.className = 'advanced-controls';
        advancedControls.innerHTML = `
            <div class="control-row">
                <button id="ttsToggle" class="control-button">🔊 TTS: OFF</button>
                <button id="clearChat" class="control-button">🗑️ Clear</button>
                <button id="exportChat" class="control-button">💾 Export</button>
                <button id="themeToggle" class="control-button">🌙 Dark</button>
            </div>
            <div class="robot-status">
                <span id="robotStatus">🤖 Robot: Disconnected</span>
                <button id="connectRobot" class="control-button">📡 Connect</button>
            </div>
        `;
        
        chatInput.appendChild(advancedControls);
        this.bindAdvancedControls();
    }

    bindAdvancedControls() {
        document.getElementById('ttsToggle').addEventListener('click', () => this.toggleTextToSpeech());
        document.getElementById('clearChat').addEventListener('click', () => this.clearConversation());
        document.getElementById('exportChat').addEventListener('click', () => this.exportConversation());
        document.getElementById('themeToggle').addEventListener('click', () => this.toggleTheme());
        document.getElementById('connectRobot').addEventListener('click', () => this.toggleRobotConnection());
    }

    setupRobotCommands() {
        // Define robot control commands that can be triggered by chat
        this.robotCommands.set('wave', {
            command: 'WAVE',
            description: 'Make the robot wave its arm',
            response: 'Waving at you! 👋'
        });
        
        this.robotCommands.set('look left', {
            command: 'LOOK_LEFT',
            description: 'Turn robot head to the left',
            response: 'Looking left now! 👈'
        });
        
        this.robotCommands.set('look right', {
            command: 'LOOK_RIGHT',
            description: 'Turn robot head to the right',
            response: 'Looking right now! 👉'
        });
        
        this.robotCommands.set('move forward', {
            command: 'F',
            description: 'Move robot forward',
            response: 'Moving forward! 🚀'
        });
        
        this.robotCommands.set('stop', {
            command: 'S',
            description: 'Stop all robot movement',
            response: 'Stopping all movement! ⛔'
        });
        
        this.robotCommands.set('dance', {
            command: 'DANCE',
            description: 'Perform a dance sequence',
            response: 'Let me show you my dance moves! 💃'
        });
    }

    initializeTextToSpeech() {
        if (this.speechSynthesis) {
            // Wait for voices to load
            if (this.speechSynthesis.getVoices().length === 0) {
                this.speechSynthesis.addEventListener('voiceschanged', () => {
                    this.selectBestVoice();
                });
            } else {
                this.selectBestVoice();
            }
        }
    }

    selectBestVoice() {
        const voices = this.speechSynthesis.getVoices();
        // Prefer English voices, then female voices for robot personality
        this.currentVoice = voices.find(voice => 
            voice.lang.startsWith('en') && voice.name.includes('Female')
        ) || voices.find(voice => 
            voice.lang.startsWith('en')
        ) || voices[0];
    }

    async sendMessage() {
        const message = this.messageInput.value.trim();
        if (!message) return;

        // Store in conversation history
        this.conversationHistory.push({
            type: 'user',
            message: message,
            timestamp: new Date().toISOString()
        });

        // Check for robot commands first
        const robotCommand = this.checkForRobotCommand(message.toLowerCase());
        if (robotCommand) {
            this.executeRobotCommand(robotCommand);
        }

        // Continue with normal AI response
        await super.sendMessage();
    }

    checkForRobotCommand(message) {
        for (let [trigger, command] of this.robotCommands) {
            if (message.includes(trigger)) {
                return command;
            }
        }
        return null;
    }

    executeRobotCommand(commandObj) {
        if (this.robotConnectionStatus) {
            // Simulate sending command to robot
            this.sendToRobot(commandObj.command);
            this.showMessage(`🤖 ${commandObj.response}`, 'bot');
        } else {
            this.showMessage(`🤖 I'd love to ${commandObj.description.toLowerCase()}, but I'm not connected to a robot right now. Try connecting first!`, 'bot');
        }
    }

    sendToRobot(command) {
        // This would integrate with the actual robot communication
        // For workshop demo, we'll simulate the command
        console.log(`🤖 Sending command to robot: ${command}`);
        
        // Simulate robot response delay
        setTimeout(() => {
            this.showMessage(`✅ Robot command "${command}" executed successfully!`, 'bot');
        }, 1000);
    }

    showMessage(message, sender) {
        super.showMessage(message, sender);
        
        // Store in conversation history
        this.conversationHistory.push({
            type: sender,
            message: message,
            timestamp: new Date().toISOString()
        });

        // Text-to-speech for bot messages
        if (sender === 'bot' && this.isTextToSpeechEnabled) {
            this.speakMessage(message);
        }
    }

    speakMessage(message) {
        if (this.speechSynthesis && this.currentVoice) {
            // Clean message for speech (remove emojis and special characters)
            const cleanMessage = message.replace(/[🤖👋👈👉🚀⛔💃✅📡]/g, '').trim();
            
            const utterance = new SpeechSynthesisUtterance(cleanMessage);
            utterance.voice = this.currentVoice;
            utterance.rate = 0.9;
            utterance.pitch = 1.1;
            utterance.volume = 0.8;
            
            this.speechSynthesis.speak(utterance);
        }
    }

    toggleTextToSpeech() {
        this.isTextToSpeechEnabled = !this.isTextToSpeechEnabled;
        const button = document.getElementById('ttsToggle');
        button.textContent = this.isTextToSpeechEnabled ? '🔊 TTS: ON' : '🔊 TTS: OFF';
        button.style.backgroundColor = this.isTextToSpeechEnabled ? '#4CAF50' : '#666';
        
        if (this.isTextToSpeechEnabled) {
            this.showMessage("Text-to-speech enabled! I'll now speak my responses. 🗣️", 'bot');
        }
    }

    clearConversation() {
        if (confirm('Are you sure you want to clear the conversation history?')) {
            this.chatMessages.innerHTML = `
                <div class="welcome-message">
                    <h3>👋 Welcome back to STEM-Xpert Robot Chatbot!</h3>
                    <p>Conversation cleared. Ready for new questions!</p>
                </div>
            `;
            this.conversationHistory = [];
            this.showMessage("Conversation cleared! What would you like to learn about? 🚀", 'bot');
        }
    }

    exportConversation() {
        if (this.conversationHistory.length === 0) {
            this.showError('No conversation to export!');
            return;
        }

        const exportData = {
            title: 'STEM-Xpert Robot Chatbot Conversation',
            exportDate: new Date().toISOString(),
            messageCount: this.conversationHistory.length,
            conversation: this.conversationHistory
        };

        const dataStr = JSON.stringify(exportData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `robot-chat-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        
        this.showMessage("📁 Conversation exported successfully! Check your downloads folder.", 'bot');
    }

    toggleTheme() {
        document.body.classList.toggle('dark-theme');
        const button = document.getElementById('themeToggle');
        const isDark = document.body.classList.contains('dark-theme');
        button.textContent = isDark ? '☀️ Light' : '🌙 Dark';
    }

    toggleRobotConnection() {
        this.robotConnectionStatus = !this.robotConnectionStatus;
        const statusElement = document.getElementById('robotStatus');
        const connectButton = document.getElementById('connectRobot');
        
        if (this.robotConnectionStatus) {
            statusElement.textContent = '🤖 Robot: Connected';
            statusElement.style.color = '#4CAF50';
            connectButton.textContent = '📡 Disconnect';
            this.showMessage("🎉 Great! I'm now connected to your robot. Try saying 'wave', 'look left', or 'move forward'!", 'bot');
        } else {
            statusElement.textContent = '🤖 Robot: Disconnected';
            statusElement.style.color = '#f44336';
            connectButton.textContent = '📡 Connect';
            this.showMessage("Robot disconnected. I can still chat with you about robotics and programming! 💬", 'bot');
        }
    }

    addRobotControlPanel() {
        const controlPanel = document.createElement('div');
        controlPanel.className = 'robot-control-panel';
        controlPanel.innerHTML = `
            <h4>🎮 Quick Robot Controls</h4>
            <div class="control-grid">
                <button class="robot-control-btn" data-command="wave">👋 Wave</button>
                <button class="robot-control-btn" data-command="look left">👈 Look Left</button>
                <button class="robot-control-btn" data-command="look right">👉 Look Right</button>
                <button class="robot-control-btn" data-command="move forward">⬆️ Forward</button>
                <button class="robot-control-btn" data-command="stop">⛔ Stop</button>
                <button class="robot-control-btn" data-command="dance">💃 Dance</button>
            </div>
        `;
        
        document.querySelector('.chat-container').appendChild(controlPanel);
        
        // Bind control panel events
        controlPanel.addEventListener('click', (e) => {
            if (e.target.classList.contains('robot-control-btn')) {
                const command = e.target.dataset.command;
                this.messageInput.value = command;
                this.sendMessage();
            }
        });
    }

    // Enhanced API call with conversation context
    async callGeminiAPI(message) {
        const recentHistory = this.conversationHistory
            .slice(-6) // Last 6 messages for context
            .map(msg => `${msg.type}: ${msg.message}`)
            .join('\n');

        const enhancedSystemPrompt = `You are a friendly AI robot assistant created by STEM-Xpert for educational robotics workshops. 
        You help students learn about robotics, programming, electronics, and STEM concepts. 
        Keep responses educational, encouraging, and age-appropriate for school students (12-18 years). 
        Be enthusiastic about robotics and learning. If asked about robotics components, programming, or workshop activities, provide helpful explanations.
        Keep responses concise but informative.
        
        You can also control a physical robot with these commands:
        - "wave" - makes the robot wave
        - "look left/right" - turns the robot's head
        - "move forward" - moves the robot forward
        - "stop" - stops all movement
        - "dance" - performs a dance sequence
        
        Recent conversation context:
        ${recentHistory}`;

        const requestBody = {
            contents: [{
                parts: [{
                    text: `${enhancedSystemPrompt}\n\nStudent question: ${message}`
                }]
            }]
        };

        const response = await fetch(`${this.apiUrl}?key=${this.apiKey}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
            throw new Error(`API request failed: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        
        if (data.candidates && data.candidates[0] && data.candidates[0].content) {
            return data.candidates[0].content.parts[0].text;
        } else {
            throw new Error('Invalid response format from API');
        }
    }
}

// CSS for advanced features
const advancedStyles = `
    .advanced-controls {
        margin-top: 10px;
        padding: 10px;
        background: #f5f5f5;
        border-radius: 8px;
        border-top: 1px solid #e0e0e0;
    }

    .control-row {
        display: flex;
        gap: 8px;
        margin-bottom: 8px;
        flex-wrap: wrap;
    }

    .robot-status {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 14px;
    }

    .control-button {
        padding: 6px 12px;
        border: none;
        border-radius: 15px;
        background: #666;
        color: white;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.3s;
    }

    .control-button:hover {
        background: #555;
        transform: scale(1.05);
    }

    .robot-control-panel {
        background: white;
        padding: 15px;
        border-top: 1px solid #e0e0e0;
    }

    .robot-control-panel h4 {
        margin: 0 0 10px 0;
        color: #333;
        text-align: center;
    }

    .control-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 8px;
    }

    .robot-control-btn {
        padding: 8px 12px;
        border: none;
        border-radius: 8px;
        background: #2196F3;
        color: white;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.3s;
    }

    .robot-control-btn:hover {
        background: #1976d2;
        transform: translateY(-2px);
    }

    /* Dark theme styles */
    .dark-theme {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    }

    .dark-theme .chat-container {
        background: #2c3e50;
        color: white;
    }

    .dark-theme .chat-messages {
        background: #34495e;
    }

    .dark-theme .message.bot .message-content {
        background: #3498db;
        color: white;
    }

    .dark-theme .advanced-controls {
        background: #34495e;
        border-color: #4a5f7a;
    }

    .dark-theme .robot-control-panel {
        background: #2c3e50;
        border-color: #4a5f7a;
        color: white;
    }
`;

// Add advanced styles to the page
const styleSheet = document.createElement('style');
styleSheet.textContent = advancedStyles;
document.head.appendChild(styleSheet);

// Function to upgrade existing chatbot to advanced version
function upgradeToAdvancedChatbot() {
    if (typeof chatbot !== 'undefined') {
        // Replace the basic chatbot with advanced version
        window.advancedChatbot = new AdvancedRobotChatbot();
        console.log('🚀 Chatbot upgraded to Advanced version!');
        console.log('✨ New features: TTS, Robot Control, Theme Toggle, Export, and more!');
        return window.advancedChatbot;
    } else {
        console.error('❌ Basic chatbot not found. Please load the main chatbot first.');
    }
}

// Auto-upgrade if basic chatbot exists
if (typeof chatbot !== 'undefined') {
    setTimeout(() => {
        upgradeToAdvancedChatbot();
    }, 1000);
}
