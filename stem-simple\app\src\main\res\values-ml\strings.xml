<resources>
    <string name="app_name">STEM സിമ്പിൾ റോബോട്ട്</string>
    
    <!-- Tab titles -->
    <string name="tab_control">നിയന്ത്രണം</string>
    <string name="tab_ai">AI സഹായി</string>
    <string name="tab_presets">പ്രീസെറ്റുകൾ</string>
    <string name="tab_settings">ക്രമീകരണങ്ങൾ</string>
    
    <!-- Control tab -->
    <string name="movement_controls">ചലന നിയന്ത്രണം</string>
    <string name="servo_controls">സെർവോ നിയന്ത്രണം</string>
    <string name="sensor_status">സെൻസർ സ്ഥിതി</string>
    <string name="forward">മുന്നോട്ട്</string>
    <string name="backward">പിന്നോട്ട്</string>
    <string name="left">ഇടത്</string>
    <string name="right">വലത്</string>
    <string name="stop">നിർത്തുക</string>
    <string name="wave">കൈ വീശുക</string>
    <string name="point">ചൂണ്ടുക</string>
    <string name="rest">വിശ്രമം</string>
    <string name="handshake">കൈ കുലുക്കൽ</string>
    <string name="arm_controls">കൈ നിയന്ത്രണം</string>
    <string name="head_controls">തല നിയന്ത്രണം</string>
    <string name="left_arm">ഇടത് കൈ</string>
    <string name="right_arm">വലത് കൈ</string>
    <string name="head_position">തല</string>
    <string name="gestures">ആംഗ്യങ്ങൾ</string>
    <string name="look_left">ഇടത്തേക്ക് നോക്കുക</string>
    <string name="look_center">മധ്യത്തിലേക്ക് നോക്കുക</string>
    <string name="look_right">വലത്തേക്ക് നോക്കുക</string>
    <string name="distance">ദൂരം: %1$s സെ.മീ</string>
    <string name="face_count">മുഖങ്ങൾ: %1$d</string>
    <string name="voice_settings">ശബ്ദ ക്രമീകരണങ്ങൾ</string>
    <string name="auto_voice_recognition">ഓട്ടോ വോയ്സ് തിരിച്ചറിയൽ</string>
    
    <!-- AI tab -->
    <string name="ai_assistant">AI സഹായി</string>
    <string name="speak_command">നിങ്ങളുടെ കമാൻഡ് പറയുക</string>
    <string name="type_command">നിങ്ങളുടെ കമാൻഡ് ടൈപ്പ് ചെയ്യുക</string>
    <string name="send">അയയ്ക്കുക</string>
    <string name="listening">കേൾക്കുന്നു...</string>
    <string name="processing">പ്രോസസ്സിംഗ്...</string>
    <string name="preset_created">പ്രീസെറ്റ് സൃഷ്ടിച്ചു: %1$s</string>
    <string name="preset_executed">പ്രീസെറ്റ് നടപ്പിലാക്കുന്നു: %1$s</string>
    
    <!-- Preset Management -->
    <string name="preset_management">പ്രീസെറ്റ് മാനേജ്മെന്റ്</string>
    <string name="create_preset">പുതിയ പ്രീസെറ്റ് സൃഷ്ടിക്കുക</string>
    <string name="preset_name">പ്രീസെറ്റ് പേര്</string>
    <string name="preset_description">വിവരണം (ഓപ്ഷണൽ)</string>
    <string name="start_recording">റെക്കോർഡിംഗ് ആരംഭിക്കുക</string>
    <string name="stop_recording">റെക്കോർഡിംഗ് നിർത്തുക</string>
    <string name="save_preset">പ്രീസെറ്റ് സേവ് ചെയ്യുക</string>
    <string name="ready_to_record">റോബോട്ട് പ്രവർത്തനങ്ങൾ റെക്കോർഡ് ചെയ്യാൻ തയ്യാർ</string>
    <string name="recording_actions">റെക്കോർഡിംഗ്... റോബോട്ട് പ്രവർത്തനങ്ങൾ നടത്തുക</string>
    <string name="saved_presets">സേവ് ചെയ്ത പ്രീസെറ്റുകൾ</string>
    <string name="refresh">പുതുക്കുക</string>
    <string name="import_preset">ഇമ്പോർട്ട്</string>
    <string name="export_all">എല്ലാം എക്സ്പോർട്ട് ചെയ്യുക</string>
    <string name="delete_all">എല്ലാം ഇല്ലാതാക്കുക</string>
    <string name="no_presets_found">പ്രീസെറ്റുകൾ കണ്ടെത്തിയില്ല</string>
    <string name="create_first_preset">നിങ്ങളുടെ ആദ്യ പ്രീസെറ്റ് സൃഷ്ടിക്കുക</string>
    <string name="execute">നടപ്പിലാക്കുക</string>
    <string name="edit">എഡിറ്റ് ചെയ്യുക</string>
    <string name="export">എക്സ്പോർട്ട്</string>
    <string name="delete">ഇല്ലാതാക്കുക</string>
    <string name="action_count">%1$d പ്രവർത്തനങ്ങൾ</string>
    
    <!-- Settings tab -->
    <string name="robot_settings">റോബോട്ട് ക്രമീകരണങ്ങൾ</string>
    <string name="ai_settings">AI ക്രമീകരണങ്ങൾ</string>
    <string name="greeting_settings">സ്മാർട്ട് ഗ്രീറ്റിംഗ് ക്രമീകരണങ്ങൾ</string>
    <string name="esp32_ip">ESP32 IP വിലാസം</string>
    <string name="greeting_enabled">സ്മാർട്ട് ഗ്രീറ്റിംഗ് പ്രവർത്തനക്ഷമമാക്കുക</string>
    <string name="greeting_distance">ഗ്രീറ്റിംഗ് ദൂരം (സെ.മീ)</string>
    <string name="greeting_cooldown">ഗ്രീറ്റിംഗ് കൂൾഡൗൺ (സെക്കൻഡ്)</string>
    <string name="ai_wake_word">AI വേക്ക് വേഡ്</string>
    <string name="language">ഭാഷ</string>
    <string name="voice_gender">ശബ്ദത്തിന്റെ ലിംഗം</string>
    <string name="male">പുരുഷൻ</string>
    <string name="female">സ്ത്രീ</string>
    
    <!-- Status messages -->
    <string name="robot_online">റോബോട്ട് ഓൺലൈൻ</string>
    <string name="robot_offline">റോബോട്ട് ഓഫ്‌ലൈൻ</string>
    <string name="connecting">കണക്റ്റ് ചെയ്യുന്നു...</string>
    <string name="connection_failed">കണക്ഷൻ പരാജയപ്പെട്ടു</string>
    <string name="camera_permission_required">ക്യാമറ അനുമതി ആവശ്യം</string>
    <string name="microphone_permission_required">മൈക്രോഫോൺ അനുമതി ആവശ്യം</string>
    
    <!-- Error messages -->
    <string name="error_camera_init">ക്യാമറ ആരംഭിക്കുന്നതിൽ പരാജയപ്പെട്ടു</string>
    <string name="error_ai_service">AI സേവനം ലഭ്യമല്ല</string>
    <string name="error_robot_communication">റോബോട്ട് കമ്മ്യൂണിക്കേഷൻ പിശക്</string>
    <string name="error_invalid_command">അസാധുവായ കമാൻഡ്</string>
</resources>
