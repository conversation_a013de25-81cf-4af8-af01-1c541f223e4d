package com.stemrobo.simple.adapters;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.stemrobo.simple.R;
import com.stemrobo.simple.ai.AICommand;

import java.util.List;

/**
 * Adapter for editing commands in preset editor
 */
public class CommandEditorAdapter extends RecyclerView.Adapter<CommandEditorAdapter.CommandViewHolder> {
    
    private final List<AICommand> commands;
    private final CommandActionListener listener;
    
    public interface CommandActionListener {
        void onCommandEdit(AICommand command, int position);
        void onCommandDelete(AICommand command, int position);
        void onCommandMoveUp(AICommand command, int position);
        void onCommandMoveDown(AICommand command, int position);
    }
    
    public CommandEditorAdapter(List<AICommand> commands, CommandActionListener listener) {
        this.commands = commands;
        this.listener = listener;
    }
    
    @NonNull
    @Override
    public CommandViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_command_editor, parent, false);
        return new CommandViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull CommandViewHolder holder, int position) {
        AICommand command = commands.get(position);
        holder.bind(command, position);
    }
    
    @Override
    public int getItemCount() {
        return commands.size();
    }
    
    class CommandViewHolder extends RecyclerView.ViewHolder {
        private final TextView commandNumber;
        private final TextView commandType;
        private final TextView commandAction;
        private final TextView commandDuration;
        private final TextView commandParameter;
        private final Button btnEdit;
        private final Button btnDelete;
        private final Button btnMoveUp;
        private final Button btnMoveDown;
        
        public CommandViewHolder(@NonNull View itemView) {
            super(itemView);
            commandNumber = itemView.findViewById(R.id.command_number);
            commandType = itemView.findViewById(R.id.command_type);
            commandAction = itemView.findViewById(R.id.command_action);
            commandDuration = itemView.findViewById(R.id.command_duration);
            commandParameter = itemView.findViewById(R.id.command_parameter);
            btnEdit = itemView.findViewById(R.id.btn_edit_command);
            btnDelete = itemView.findViewById(R.id.btn_delete_command);
            btnMoveUp = itemView.findViewById(R.id.btn_move_up);
            btnMoveDown = itemView.findViewById(R.id.btn_move_down);
        }
        
        public void bind(AICommand command, int position) {
            commandNumber.setText(String.valueOf(position + 1));
            commandType.setText(command.getType());
            commandAction.setText(command.getAction());
            
            // Duration
            if (command.getDuration() > 0) {
                commandDuration.setText(command.getDuration() + "ms");
                commandDuration.setVisibility(View.VISIBLE);
            } else {
                commandDuration.setVisibility(View.GONE);
            }
            
            // Parameter
            if (command.getParameter() != null && !command.getParameter().isEmpty()) {
                commandParameter.setText(command.getParameter());
                commandParameter.setVisibility(View.VISIBLE);
            } else {
                commandParameter.setVisibility(View.GONE);
            }
            
            // Button states
            btnMoveUp.setEnabled(position > 0);
            btnMoveDown.setEnabled(position < commands.size() - 1);
            
            // Click listeners
            btnEdit.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onCommandEdit(command, position);
                }
            });
            
            btnDelete.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onCommandDelete(command, position);
                }
            });
            
            btnMoveUp.setOnClickListener(v -> {
                if (listener != null && position > 0) {
                    listener.onCommandMoveUp(command, position);
                }
            });
            
            btnMoveDown.setOnClickListener(v -> {
                if (listener != null && position < commands.size() - 1) {
                    listener.onCommandMoveDown(command, position);
                }
            });
        }
    }
}
