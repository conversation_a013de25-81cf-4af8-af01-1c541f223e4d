<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Robot Settings -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/robot_settings"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="16dp" />

                <!-- ESP32 IP Address -->
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:hint="@string/esp32_ip"
                    app:boxStrokeColor="@color/primary_blue"
                    app:hintTextColor="@color/primary_blue">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/esp32_ip_input"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textUri"
                        android:text="***********" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- Connection Status -->
                <TextView
                    android:id="@+id/connection_status_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/robot_offline"
                    android:textColor="@color/status_offline"
                    android:textSize="14sp"
                    android:layout_marginBottom="8dp" />

                <!-- Test Connection Button -->
                <Button
                    android:id="@+id/btn_test_connection"
                    style="@style/ButtonSecondary"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Test Connection" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Smart Greeting Settings -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/greeting_settings"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="16dp" />

                <!-- Enable Smart Greeting -->
                <com.google.android.material.switchmaterial.SwitchMaterial
                    android:id="@+id/smart_greeting_switch"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/greeting_enabled"
                    android:checked="true"
                    android:layout_marginBottom="16dp"
                    app:thumbTint="@color/primary_blue"
                    app:trackTint="@color/primary_blue" />

                <!-- Greeting Distance -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/greeting_distance"
                    android:textSize="16sp"
                    android:layout_marginBottom="8dp" />

                <SeekBar
                    android:id="@+id/greeting_distance_seekbar"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:max="100"
                    android:progress="30"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/greeting_distance_value"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="30 cm"
                    android:textSize="14sp"
                    android:gravity="center"
                    android:layout_marginBottom="16dp" />

                <!-- Greeting Cooldown -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/greeting_cooldown"
                    android:textSize="16sp"
                    android:layout_marginBottom="8dp" />

                <SeekBar
                    android:id="@+id/greeting_cooldown_seekbar"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:max="60"
                    android:progress="10"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/greeting_cooldown_value"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="10 seconds"
                    android:textSize="14sp"
                    android:gravity="center"
                    android:layout_marginBottom="16dp" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- AI Settings -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/ai_settings"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="16dp" />

                <!-- AI Wake Word -->
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:hint="@string/ai_wake_word"
                    app:boxStrokeColor="@color/primary_blue"
                    app:hintTextColor="@color/primary_blue">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/wake_word_input"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="text"
                        android:text="Hey Robot" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- Language Selection -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/language"
                    android:textSize="16sp"
                    android:layout_marginBottom="8dp" />

                <Spinner
                    android:id="@+id/language_spinner"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp" />

                <!-- Voice Gender -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/voice_gender"
                    android:textSize="16sp"
                    android:layout_marginBottom="8dp" />

                <RadioGroup
                    android:id="@+id/voice_gender_group"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="16dp">

                    <RadioButton
                        android:id="@+id/voice_female"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/female"
                        android:checked="true" />

                    <RadioButton
                        android:id="@+id/voice_male"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/male" />

                </RadioGroup>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- AI Settings -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/ai_settings"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="16dp" />

                <!-- Context Memory Size -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/ai_context_memory"
                    android:textSize="16sp"
                    android:layout_marginBottom="8dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="16dp">

                    <SeekBar
                        android:id="@+id/ai_context_size_seekbar"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:max="50"
                        android:progress="10" />

                    <TextView
                        android:id="@+id/ai_context_size_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="10"
                        android:textSize="14sp"
                        android:minWidth="40dp"
                        android:gravity="center"
                        android:layout_marginStart="8dp" />

                </LinearLayout>

                <!-- AI Personality -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/ai_personality"
                    android:textSize="16sp"
                    android:layout_marginBottom="8dp" />

                <Spinner
                    android:id="@+id/ai_personality_spinner"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp" />

                <!-- Response Style -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/ai_response_style"
                    android:textSize="16sp"
                    android:layout_marginBottom="8dp" />

                <Spinner
                    android:id="@+id/ai_response_style_spinner"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp" />

                <!-- Verbose Mode -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="16dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/ai_verbose_mode"
                        android:textSize="16sp" />

                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/ai_verbose_mode_switch"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                </LinearLayout>

                <!-- Clear AI History -->
                <Button
                    android:id="@+id/btn_clear_ai_history"
                    style="@style/ButtonSecondary"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/clear_ai_history"
                    android:layout_marginBottom="8dp" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Movement Duration Settings -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/movement_duration_settings"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="16dp" />

                <!-- Forward Duration -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/forward_duration"
                        android:textSize="16sp" />

                    <SeekBar
                        android:id="@+id/forward_duration_seekbar"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="2"
                        android:max="10"
                        android:progress="3" />

                    <TextView
                        android:id="@+id/forward_duration_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="3s"
                        android:textSize="14sp"
                        android:minWidth="40dp"
                        android:gravity="center"
                        android:layout_marginStart="8dp" />

                </LinearLayout>

                <!-- Backward Duration -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/backward_duration"
                        android:textSize="16sp" />

                    <SeekBar
                        android:id="@+id/backward_duration_seekbar"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="2"
                        android:max="10"
                        android:progress="3" />

                    <TextView
                        android:id="@+id/backward_duration_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="3s"
                        android:textSize="14sp"
                        android:minWidth="40dp"
                        android:gravity="center"
                        android:layout_marginStart="8dp" />

                </LinearLayout>

                <!-- Left Duration -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/left_duration"
                        android:textSize="16sp" />

                    <SeekBar
                        android:id="@+id/left_duration_seekbar"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="2"
                        android:max="10"
                        android:progress="2" />

                    <TextView
                        android:id="@+id/left_duration_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="2s"
                        android:textSize="14sp"
                        android:minWidth="40dp"
                        android:gravity="center"
                        android:layout_marginStart="8dp" />

                </LinearLayout>

                <!-- Right Duration -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/right_duration"
                        android:textSize="16sp" />

                    <SeekBar
                        android:id="@+id/right_duration_seekbar"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="2"
                        android:max="10"
                        android:progress="2" />

                    <TextView
                        android:id="@+id/right_duration_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="2s"
                        android:textSize="14sp"
                        android:minWidth="40dp"
                        android:gravity="center"
                        android:layout_marginStart="8dp" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Servo Angle Settings -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/servo_angle_settings"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="16dp" />

                <!-- Servo Speed -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Servo Speed"
                        android:textSize="16sp" />

                    <SeekBar
                        android:id="@+id/servo_speed_seekbar"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="2"
                        android:max="100"
                        android:progress="50" />

                    <TextView
                        android:id="@+id/servo_speed_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="50%"
                        android:textSize="14sp"
                        android:minWidth="50dp"
                        android:gravity="center"
                        android:layout_marginStart="8dp" />

                </LinearLayout>

                <!-- Handshake Angle -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/handshake_angle"
                        android:textSize="16sp" />

                    <SeekBar
                        android:id="@+id/handshake_angle_seekbar"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="2"
                        android:max="180"
                        android:progress="90" />

                    <TextView
                        android:id="@+id/handshake_angle_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="90°"
                        android:textSize="14sp"
                        android:minWidth="50dp"
                        android:gravity="center"
                        android:layout_marginStart="8dp" />

                </LinearLayout>

                <!-- Wave Angle -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/wave_angle"
                        android:textSize="16sp" />

                    <SeekBar
                        android:id="@+id/wave_angle_seekbar"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="2"
                        android:max="180"
                        android:progress="120" />

                    <TextView
                        android:id="@+id/wave_angle_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="120°"
                        android:textSize="14sp"
                        android:minWidth="50dp"
                        android:gravity="center"
                        android:layout_marginStart="8dp" />

                </LinearLayout>

                <!-- Point Angle -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Walking Angle"
                        android:textSize="16sp" />

                    <SeekBar
                        android:id="@+id/walking_angle_seekbar"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="2"
                        android:max="180"
                        android:progress="90" />

                    <TextView
                        android:id="@+id/walking_angle_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="90°"
                        android:textSize="14sp"
                        android:minWidth="50dp"
                        android:gravity="center"
                        android:layout_marginStart="8dp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/point_angle"
                        android:textSize="16sp" />

                    <SeekBar
                        android:id="@+id/point_angle_seekbar"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="2"
                        android:max="180"
                        android:progress="180" />

                    <TextView
                        android:id="@+id/point_angle_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="180°"
                        android:textSize="14sp"
                        android:minWidth="50dp"
                        android:gravity="center"
                        android:layout_marginStart="8dp" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Advanced Settings -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Advanced Settings"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="16dp" />

                <!-- Microphone Sensitivity -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="16dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Microphone Sensitivity"
                        android:textSize="14sp" />

                    <SeekBar
                        android:id="@+id/mic_sensitivity_seekbar"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="2"
                        android:max="100"
                        android:progress="50" />

                    <TextView
                        android:id="@+id/mic_sensitivity_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="50%"
                        android:textSize="14sp"
                        android:minWidth="50dp"
                        android:gravity="center"
                        android:layout_marginStart="8dp" />

                </LinearLayout>

                <!-- Microphone Duration -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="16dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Voice Recording Duration"
                        android:textSize="14sp" />

                    <SeekBar
                        android:id="@+id/mic_duration_seekbar"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="2"
                        android:max="100"
                        android:progress="40" />

                    <TextView
                        android:id="@+id/mic_duration_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="4.0s"
                        android:textSize="14sp"
                        android:minWidth="50dp"
                        android:gravity="center"
                        android:layout_marginStart="8dp" />

                </LinearLayout>

                <!-- Face Detection Duration -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="16dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Face Detection Duration"
                        android:textSize="14sp" />

                    <SeekBar
                        android:id="@+id/face_detection_duration_seekbar"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="2"
                        android:max="95"
                        android:progress="35" />

                    <TextView
                        android:id="@+id/face_detection_duration_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="4.0s"
                        android:textSize="14sp"
                        android:minWidth="50dp"
                        android:gravity="center"
                        android:layout_marginStart="8dp" />

                </LinearLayout>

                <!-- Handshake Duration -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="16dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Handshake Duration"
                        android:textSize="14sp" />

                    <SeekBar
                        android:id="@+id/handshake_duration_seekbar"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="2"
                        android:max="29"
                        android:progress="4" />

                    <TextView
                        android:id="@+id/handshake_duration_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="5s"
                        android:textSize="14sp"
                        android:minWidth="50dp"
                        android:gravity="center"
                        android:layout_marginStart="8dp" />

                </LinearLayout>

                <!-- Out of Range Reset Time -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginTop="16dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Out of Range Reset Time"
                        android:textSize="14sp" />

                    <SeekBar
                        android:id="@+id/out_of_range_reset_seekbar"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="2"
                        android:max="59"
                        android:progress="9" />

                    <TextView
                        android:id="@+id/out_of_range_reset_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="10s"
                        android:textSize="14sp"
                        android:minWidth="50dp"
                        android:gravity="center"
                        android:layout_marginStart="8dp" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Save Settings Button -->
        <Button
            android:id="@+id/btn_save_settings"
            style="@style/ButtonPrimary"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Save Settings"
            android:layout_marginTop="16dp" />

    </LinearLayout>

</ScrollView>
