/*
 * STEM Simple Robot Controller - Enhanced ESP32 Firmware
 * Enhanced version for educational robotics with advanced features:
 * - Two-wheel movement (forward, backward, left, right, stop)
 * - Servo control (left arm, right arm, head)
 * - Ultrasonic sensor for distance measurement
 * - USB Serial communication (primary)
 * - WiFi Access Point for web-based smartphone control
 * - Web interface for remote control
 * - Handshake and gesture controls
 */

#include <WiFi.h>
#include <WebServer.h>
#include <ESP32Servo.h>
#include <ArduinoJson.h>
#include <SPIFFS.h>

// ===== CONFIGURATION =====
// WiFi Access Point Settings
const char* AP_SSID = "STEM-Simple-Robot";
const char* AP_PASSWORD = "robot123";
const IPAddress AP_IP(192, 168, 4, 1);
const IPAddress AP_GATEWAY(192, 168, 4, 1);
const IPAddress AP_SUBNET(255, 255, 255, 0);

// Hardware Pin Definitions
// Two-wheel robot motor pins
const int MOTOR_LEFT_PIN1 = 26;   // Left motor direction pin 1
const int MOTOR_LEFT_PIN2 = 27;   // Left motor direction pin 2
const int MOTOR_RIGHT_PIN1 = 32;  // Right motor direction pin 1
const int MOTOR_RIGHT_PIN2 = 33;  // Right motor direction pin 2

// Servo pins
const int SERVO_LEFT_ARM = 18;    // Left arm servo
const int SERVO_RIGHT_ARM = 19;   // Right arm servo
const int SERVO_HEAD = 21;        // Head servo (new)

// Ultrasonic sensor pins
const int ULTRASONIC_TRIG_PIN = 12;
const int ULTRASONIC_ECHO_PIN = 13;

// Status LED
const int STATUS_LED = 2;

// ===== GLOBAL VARIABLES =====
WebServer server(80);
Servo leftArmServo;
Servo rightArmServo;
Servo headServo;

// Robot state variables
bool isMoving = false;
bool isHandshaking = false;
unsigned long handshakeStartTime = 0;
int handshakeDuration = 5000; // Default 5 seconds
int leftArmRestPosition = 180;  // Left arm rest position
int rightArmRestPosition = 0;   // Right arm rest position
int headCenterPosition = 90;    // Head center position

// Movement timing
unsigned long movementStartTime = 0;
int movementDuration = 0;
String currentMovement = "";

// Servo positions
int currentLeftArmPos = 180;
int currentRightArmPos = 0;
int currentHeadPos = 90;

// Servo positions
int leftArmPosition = 180;   // Rest position for left arm
int rightArmPosition = 0;    // Rest position for right arm
int headPosition = 90;       // Center position for head

// Communication mode
bool usbMode = true;         // Primary communication via USB
bool wifiEnabled = true;     // Web interface enabled

// Movement timing
unsigned long lastCommandTime = 0;
const unsigned long COMMAND_TIMEOUT = 3000; // 3 seconds

// Servo speed
int servoMoveDelay = 1; // Default to fastest speed
int walkingAngle = 90; // Default walking angle

// ===== SETUP =====
void setup() {
  // Initialize Serial for USB communication (primary)
  Serial.begin(115200);
  Serial.println("STEM Simple Robot Controller - Enhanced Version");
  Serial.println("USB Serial communication initialized (primary mode)");
  
  // Initialize hardware pins
  setupMotorPins();
  setupServos();
  setupSensors();
  setupStatusLED();
  
  // Initialize WiFi Access Point for web control
  setupWiFiAP();
  
  // Initialize web server
  setupWebServer();
  
  // Initialize SPIFFS for web files
  if (!SPIFFS.begin(true)) {
    Serial.println("SPIFFS initialization failed");
  }
  
  Serial.println("System ready - USB primary, WiFi web interface available");
  Serial.println("Commands: MOVE_FORWARD, MOVE_BACKWARD, TURN_LEFT, TURN_RIGHT, STOP");
  Serial.println("Servos: ARM_LEFT_[0-180], ARM_RIGHT_[0-180], HEAD_[0-180]");
  Serial.println("Gestures: WAVE, POINT, REST, HANDSHAKE, LOOK_LEFT, LOOK_RIGHT");
  Serial.println("Sensors: GET_DISTANCE, GET_STATUS");
}

// ===== MAIN LOOP =====
void loop() {
  // Handle web server requests
  server.handleClient();
  
  // Handle USB serial commands (primary)
  handleSerialCommands();
  
  // Auto-stop movement after timeout
  if (millis() - lastCommandTime > COMMAND_TIMEOUT) {
    stopMovement();
  }
  
  // Status LED heartbeat
  updateStatusLED();
  
  // Send ultrasonic distance measurement periodically
  sendDistanceMeasurement();

  delay(10);
}

// ===== HARDWARE SETUP =====
void setupMotorPins() {
  pinMode(MOTOR_LEFT_PIN1, OUTPUT);
  pinMode(MOTOR_LEFT_PIN2, OUTPUT);
  pinMode(MOTOR_RIGHT_PIN1, OUTPUT);
  pinMode(MOTOR_RIGHT_PIN2, OUTPUT);
  stopMovement();
  Serial.println("Motor pins initialized");
}

void setupServos() {
  leftArmServo.attach(SERVO_LEFT_ARM);
  rightArmServo.attach(SERVO_RIGHT_ARM);
  headServo.attach(SERVO_HEAD);
  
  // Move to rest positions
  moveToRestPosition();
  Serial.println("Servos initialized and moved to rest position");
}

void setupSensors() {
  pinMode(ULTRASONIC_TRIG_PIN, OUTPUT);
  pinMode(ULTRASONIC_ECHO_PIN, INPUT);
  Serial.println("Ultrasonic sensor initialized");
}

void setupStatusLED() {
  pinMode(STATUS_LED, OUTPUT);
  digitalWrite(STATUS_LED, LOW);
  Serial.println("Status LED initialized");
}

void setupWiFiAP() {
  WiFi.mode(WIFI_AP);
  WiFi.softAPConfig(AP_IP, AP_GATEWAY, AP_SUBNET);
  WiFi.softAP(AP_SSID, AP_PASSWORD);
  
  Serial.println("WiFi Access Point started");
  Serial.print("SSID: ");
  Serial.println(AP_SSID);
  Serial.print("IP: ");
  Serial.println(WiFi.softAPIP());
  Serial.println("Web interface available for smartphone control");
}

// ===== SERIAL COMMAND HANDLING =====
void handleSerialCommands() {
  if (Serial.available()) {
    String command = Serial.readStringUntil('\n');
    command.trim();
    command.toUpperCase();
    
    Serial.println("USB Command received: " + command);
    executeCommand(command);
  }
}

void executeCommand(String command) {
  lastCommandTime = millis();
  
  // Movement commands
  if (command == "MOVE_FORWARD" || command == "FORWARD") {
    moveForward();
    Serial.println("OK: Moving forward");
  }
  else if (command == "MOVE_BACKWARD" || command == "BACKWARD") {
    moveBackward();
    Serial.println("OK: Moving backward");
  }
  else if (command == "TURN_LEFT" || command == "LEFT") {
    turnLeft();
    Serial.println("OK: Turning left");
  }
  else if (command == "TURN_RIGHT" || command == "RIGHT") {
    turnRight();
    Serial.println("OK: Turning right");
  }
  else if (command == "STOP") {
    stopMovement();
    Serial.println("OK: Stopped");
  }
  
  // Servo position commands
  else if (command.startsWith("ARM_LEFT_")) {
    int pos = command.substring(9).toInt();
    setLeftArmPosition(pos);
    Serial.println("OK: Left arm position " + String(pos));
  }
  else if (command.startsWith("ARM_RIGHT_")) {
    int pos = command.substring(10).toInt();
    setRightArmPosition(pos);
    Serial.println("OK: Right arm position " + String(pos));
  }
  else if (command.startsWith("HEAD_")) {
    int pos = command.substring(5).toInt();
    setHeadPosition(pos);
    Serial.println("OK: Head position " + String(pos));
  }
  else if (command.startsWith("SERVO_SPEED_")) {
    int delayVal = command.substring(12).toInt();
    servoMoveDelay = constrain(delayVal, 1, 20); // Constrain delay between 1ms and 20ms
    Serial.println("OK: Servo speed set to delay " + String(servoMoveDelay) + "ms");
  }
  else if (command.startsWith("WALK_ANGLE_")) {
    walkingAngle = command.substring(11).toInt();
    Serial.println("OK: Walking angle set to " + String(walkingAngle));
  }
  
  // Gesture commands
  else if (command.startsWith("WAVE_")) {
    int angle = command.substring(5).toInt();
    performWave(angle);
    Serial.println("OK: Waving at " + String(angle) + "°");
  }
  else if (command == "WAVE") {
    performWave();
    Serial.println("OK: Waving");
  }
  else if (command.startsWith("POINT_")) {
    int angle = command.substring(6).toInt();
    performPoint(angle);
    Serial.println("OK: Pointing at " + String(angle) + "°");
  }
  else if (command == "POINT") {
    performPoint(180); // Default angle
    Serial.println("OK: Pointing");
  }
  else if (command == "REST") {
    moveToRestPosition();
    Serial.println("OK: Rest position");
  }
  else if (command.startsWith("HANDSHAKE_")) {
    int angle = command.substring(10).toInt();
    performHandshake(angle);
    Serial.println("OK: Handshake at " + String(angle) + "°");
  }
  else if (command == "HANDSHAKE") {
    performHandshake(90); // Default angle
    Serial.println("OK: Handshake gesture");
  }
  else if (command.startsWith("HANDSHAKE_DURATION_")) {
    int duration = command.substring(19).toInt();
    if (duration > 0 && duration <= 30000) { // Max 30 seconds
      handshakeDuration = duration;
      Serial.println("OK: Handshake duration set to " + String(duration) + "ms");
    } else {
      Serial.println("ERROR: Invalid handshake duration");
    }
  }
  else if (command == "LOOK_LEFT") {
    lookLeft();
    Serial.println("OK: Looking left");
  }
  else if (command == "LOOK_RIGHT") {
    lookRight();
    Serial.println("OK: Looking right");
  }
  else if (command == "LOOK_CENTER") {
    lookCenter();
    Serial.println("OK: Looking center");
  }
  
  // Sensor commands
  else if (command == "GET_DISTANCE") {
    float distance = getDistance();
    Serial.println("DISTANCE:" + String(distance, 1));
  }
  else if (command == "GET_STATUS") {
    sendStatus();
  }
  
  else {
    Serial.println("ERROR: Unknown command - " + command);
  }
}

// ===== MOVEMENT FUNCTIONS =====
void moveForward() {
  digitalWrite(MOTOR_LEFT_PIN1, HIGH);
  digitalWrite(MOTOR_LEFT_PIN2, LOW);
  digitalWrite(MOTOR_RIGHT_PIN1, HIGH);
  digitalWrite(MOTOR_RIGHT_PIN2, LOW);
  // Set arms to walking position
  smoothServoMove(leftArmServo, currentLeftArmPos, walkingAngle, servoMoveDelay);
  smoothServoMove(rightArmServo, currentRightArmPos, 180 - walkingAngle, servoMoveDelay);
  currentLeftArmPos = walkingAngle;
  currentRightArmPos = 180 - walkingAngle;
}

void moveBackward() {
  digitalWrite(MOTOR_LEFT_PIN1, LOW);
  digitalWrite(MOTOR_LEFT_PIN2, HIGH);
  digitalWrite(MOTOR_RIGHT_PIN1, LOW);
  digitalWrite(MOTOR_RIGHT_PIN2, HIGH);
  // Set arms to walking position
  smoothServoMove(leftArmServo, currentLeftArmPos, walkingAngle, servoMoveDelay);
  smoothServoMove(rightArmServo, currentRightArmPos, 180 - walkingAngle, servoMoveDelay);
  currentLeftArmPos = walkingAngle;
  currentRightArmPos = 180 - walkingAngle;
}

void turnLeft() {
  digitalWrite(MOTOR_LEFT_PIN1, LOW);
  digitalWrite(MOTOR_LEFT_PIN2, HIGH);
  digitalWrite(MOTOR_RIGHT_PIN1, HIGH);
  digitalWrite(MOTOR_RIGHT_PIN2, LOW);
}

void turnRight() {
  digitalWrite(MOTOR_LEFT_PIN1, HIGH);
  digitalWrite(MOTOR_LEFT_PIN2, LOW);
  digitalWrite(MOTOR_RIGHT_PIN1, LOW);
  digitalWrite(MOTOR_RIGHT_PIN2, HIGH);
}

void stopMovement() {
  digitalWrite(MOTOR_LEFT_PIN1, LOW);
  digitalWrite(MOTOR_LEFT_PIN2, LOW);
  digitalWrite(MOTOR_RIGHT_PIN1, LOW);
  digitalWrite(MOTOR_RIGHT_PIN2, LOW);
  // Return arms to rest position
  moveToRestPosition();
}

// ===== SERVO FUNCTIONS =====
void setLeftArmPosition(int position) {
  position = constrain(position, 0, 180);
  leftArmPosition = position;
  leftArmServo.write(position);
  delay(1);
}

void setRightArmPosition(int position) {
  position = constrain(position, 0, 180);
  rightArmPosition = position;
  rightArmServo.write(position);
  delay(1);
}

void setHeadPosition(int position) {
  position = constrain(position, 0, 180);
  headPosition = position;
  headServo.write(position);
  delay(1);
}

void moveToRestPosition() {
  // Smooth movement to rest positions
  smoothServoMove(leftArmServo, currentLeftArmPos, leftArmRestPosition, 10);
  smoothServoMove(rightArmServo, currentRightArmPos, rightArmRestPosition, 10);
  smoothServoMove(headServo, currentHeadPos, headCenterPosition, 10);

  // Update current positions
  currentLeftArmPos = leftArmRestPosition;
  currentRightArmPos = rightArmRestPosition;
  currentHeadPos = headCenterPosition;
}

// ===== GESTURE FUNCTIONS =====
void performWave(int angle) {
  // Smooth right arm wave
  smoothServoMove(rightArmServo, currentRightArmPos, angle, servoMoveDelay);
  currentRightArmPos = angle;

  for (int i = 0; i < 3; i++) {
    smoothServoMove(rightArmServo, angle, angle - 45, servoMoveDelay);
    delay(50);
    smoothServoMove(rightArmServo, angle - 45, angle, servoMoveDelay);
    delay(50);
  }

  // Return to rest smoothly
  smoothServoMove(rightArmServo, currentRightArmPos, rightArmRestPosition, servoMoveDelay);
  currentRightArmPos = rightArmRestPosition;
}

void performWave() {
  performWave(120); // Default angle
}

void performPoint(int angle) {
  // Smooth pointing gesture
  smoothServoMove(rightArmServo, currentRightArmPos, angle, servoMoveDelay);
  currentRightArmPos = angle;
  delay(200);

  // Return to rest smoothly
  smoothServoMove(rightArmServo, currentRightArmPos, rightArmRestPosition, servoMoveDelay);
  currentRightArmPos = rightArmRestPosition;
}

void performPoint() {
  performPoint(180); // Default angle
}

void performHandshake(int angle) {
  performHandshakeWithDuration(handshakeDuration, angle);
}

void performHandshake() {
  performHandshakeWithDuration(handshakeDuration, 90); // Default angle
}

void performHandshakeWithDuration(int duration, int angle) {
  isHandshaking = true;
  handshakeStartTime = millis();

  // Smooth movement to handshake position
  smoothServoMove(rightArmServo, currentRightArmPos, angle, servoMoveDelay);
  currentRightArmPos = angle;

  Serial.println("Handshake started for " + String(duration) + "ms");

  // Hold handshake position for specified duration
  delay(duration);

  // Gentle shake motion
  for (int i = 0; i < 2; i++) {
    smoothServoMove(rightArmServo, angle, angle - 5, servoMoveDelay);
    delay(20);
    smoothServoMove(rightArmServo, angle - 5, angle + 5, servoMoveDelay);
    delay(20);
  }

  // Return to rest position smoothly
  smoothServoMove(rightArmServo, currentRightArmPos, rightArmRestPosition, servoMoveDelay);
  currentRightArmPos = rightArmRestPosition;

  isHandshaking = false;
  Serial.println("Handshake completed");
}

void smoothServoMove(Servo &servo, int startPos, int endPos, int delayMs) {
  int step = (startPos < endPos) ? 1 : -1;
  for (int pos = startPos; pos != endPos; pos += step) {
    servo.write(pos);
    delay(servoMoveDelay); // Use configurable delay
  }
  servo.write(endPos);
}

void lookLeft() {
  setHeadPosition(150);
}

void lookRight() {
  setHeadPosition(30);
}

void lookCenter() {
  setHeadPosition(90);
}

// ===== SENSOR FUNCTIONS =====
void sendDistanceMeasurement() {
  static unsigned long lastDistanceSendTime = 0;
  const unsigned long distanceSendInterval = 500; // Send every 500ms

  if (millis() - lastDistanceSendTime >= distanceSendInterval) {
    lastDistanceSendTime = millis();
    float distance = getDistance();
    if (distance != 999.0) { // Only send valid readings
        Serial.println("DISTANCE:" + String(distance, 1));
    }
  }
}

float getDistance() {
  // Clear the trigger pin
  digitalWrite(ULTRASONIC_TRIG_PIN, LOW);
  delayMicroseconds(2);

  // Send a 10 microsecond pulse
  digitalWrite(ULTRASONIC_TRIG_PIN, HIGH);
  delayMicroseconds(10);
  digitalWrite(ULTRASONIC_TRIG_PIN, LOW);

  // Read the echo pin with longer timeout for better reliability
  long duration = pulseIn(ULTRASONIC_ECHO_PIN, HIGH, 50000); // 50ms timeout

  if (duration == 0) {
    return 999.0; // No echo received
  }

  // Calculate distance in cm
  float distance = (duration * 0.034) / 2;

  // Validate distance (HC-SR04 range: 2-400cm)
  if (distance < 2 || distance > 400) {
    return 999.0;
  }

  return distance;
}

void sendStatus() {
  DynamicJsonDocument status(512);
  status["leftArm"] = leftArmPosition;
  status["rightArm"] = rightArmPosition;
  status["head"] = headPosition;
  status["distance"] = getDistance();
  status["wifi"] = WiFi.softAPgetStationNum();
  status["uptime"] = millis();
  
  String statusStr;
  serializeJson(status, statusStr);
  Serial.println("STATUS:" + statusStr);
}

// ===== WEB SERVER SETUP =====
void setupWebServer() {
  // Serve main control page
  server.on("/", handleRoot);

  // API endpoints
  server.on("/api/move", HTTP_POST, handleMoveAPI);
  server.on("/api/servo", HTTP_POST, handleServoAPI);
  server.on("/api/gesture", HTTP_POST, handleGestureAPI);
  server.on("/api/status", HTTP_GET, handleStatusAPI);
  server.on("/api/distance", HTTP_GET, handleDistanceAPI);

  // Start server
  server.begin();
  Serial.println("Web server started on port 80");
}

// ===== WEB HANDLERS =====
void handleRoot() {
  String html = getWebInterface();
  server.send(200, "text/html", html);
}

void handleMoveAPI() {
  if (server.hasArg("direction")) {
    String direction = server.arg("direction");
    direction.toUpperCase();

    if (direction == "FORWARD") moveForward();
    else if (direction == "BACKWARD") moveBackward();
    else if (direction == "LEFT") turnLeft();
    else if (direction == "RIGHT") turnRight();
    else if (direction == "STOP") stopMovement();

    lastCommandTime = millis();
    server.send(200, "application/json", "{\"status\":\"ok\",\"command\":\"" + direction + "\"}");
  } else {
    server.send(400, "application/json", "{\"status\":\"error\",\"message\":\"Missing direction\"}");
  }
}

void handleServoAPI() {
  if (server.hasArg("servo") && server.hasArg("position")) {
    String servo = server.arg("servo");
    int position = server.arg("position").toInt();

    if (servo == "leftArm") setLeftArmPosition(position);
    else if (servo == "rightArm") setRightArmPosition(position);
    else if (servo == "head") setHeadPosition(position);

    server.send(200, "application/json", "{\"status\":\"ok\",\"servo\":\"" + servo + "\",\"position\":" + String(position) + "}");
  } else {
    server.send(400, "application/json", "{\"status\":\"error\",\"message\":\"Missing parameters\"}");
  }
}

void handleGestureAPI() {
  if (server.hasArg("gesture")) {
    String gesture = server.arg("gesture");
    gesture.toUpperCase();

    if (gesture == "WAVE") performWave();
    else if (gesture == "POINT") performPoint();
    else if (gesture == "HANDSHAKE") performHandshake();
    else if (gesture == "REST") moveToRestPosition();
    else if (gesture == "LOOK_LEFT") lookLeft();
    else if (gesture == "LOOK_RIGHT") lookRight();
    else if (gesture == "LOOK_CENTER") lookCenter();

    server.send(200, "application/json", "{\"status\":\"ok\",\"gesture\":\"" + gesture + "\"}");
  } else {
    server.send(400, "application/json", "{\"status\":\"error\",\"message\":\"Missing gesture\"}");
  }
}

void handleStatusAPI() {
  DynamicJsonDocument status(512);
  status["leftArm"] = leftArmPosition;
  status["rightArm"] = rightArmPosition;
  status["head"] = headPosition;
  status["distance"] = getDistance();
  status["wifi"] = WiFi.softAPgetStationNum();
  status["uptime"] = millis();

  String statusStr;
  serializeJson(status, statusStr);
  server.send(200, "application/json", statusStr);
}

void handleDistanceAPI() {
  float distance = getDistance();
  server.send(200, "application/json", "{\"distance\":" + String(distance, 1) + "}");
}

// ===== WEB INTERFACE HTML =====
String getWebInterface() {
  return R"HTML(
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>STEM Simple Robot Control</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f0f0f0; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { text-align: center; color: #333; margin-bottom: 30px; }
        .section { margin-bottom: 30px; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
        .section h3 { margin-top: 0; color: #555; }
        .controls { display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 10px; }
        .btn { padding: 15px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; transition: all 0.3s; }
        .btn-move { background: #4CAF50; color: white; }
        .btn-move:hover { background: #45a049; }
        .btn-gesture { background: #2196F3; color: white; }
        .btn-gesture:hover { background: #1976D2; }
        .btn-stop { background: #f44336; color: white; }
        .btn-stop:hover { background: #d32f2f; }
        .slider-container { margin: 10px 0; }
        .slider { width: 100%; margin: 10px 0; }
        .status { background: #e8f5e8; padding: 10px; border-radius: 5px; margin-top: 20px; }
        .distance { font-size: 18px; font-weight: bold; color: #2196F3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>&#129302; STEM Simple Robot</h1>

        <div class="section">
            <h3>Movement Control</h3>
            <div class="controls">
                <button class="btn btn-move" onclick="sendMove('forward')">&uarr; Forward</button>
                <button class="btn btn-move" onclick="sendMove('backward')">&darr; Backward</button>
                <button class="btn btn-move" onclick="sendMove('left')">&larr; Left</button>
                <button class="btn btn-move" onclick="sendMove('right')">&rarr; Right</button>
                <button class="btn btn-stop" onclick="sendMove('stop')">&#128721; Stop</button>
            </div>
        </div>

        <div class="section">
            <h3>Arm Control</h3>
            <div class="slider-container">
                <label>Left Arm: <span id="leftArmValue">180</span>&deg;</label>
                <input type="range" class="slider" min="0" max="180" value="180" id="leftArmSlider" oninput="updateServo('leftArm', this.value)">
            </div>
            <div class="slider-container">
                <label>Right Arm: <span id="rightArmValue">0</span>&deg;</label>
                <input type="range" class="slider" min="0" max="180" value="0" id="rightArmSlider" oninput="updateServo('rightArm', this.value)">
            </div>
        </div>

        <div class="section">
            <h3>Head Control</h3>
            <div class="slider-container">
                <label>Head Position: <span id="headValue">90</span>&deg;</label>
                <input type="range" class="slider" min="0" max="180" value="90" id="headSlider" oninput="updateServo('head', this.value)">
            </div>
            <div class="controls">
                <button class="btn btn-gesture" onclick="sendGesture('look_left')">&#128072; Look Left</button>
                <button class="btn btn-gesture" onclick="sendGesture('look_center')">&#128064; Look Center</button>
                <button class="btn btn-gesture" onclick="sendGesture('look_right')">&#128073; Look Right</button>
            </div>
        </div>

        <div class="section">
            <h3>Gestures</h3>
            <div class="controls">
                <button class="btn btn-gesture" onclick="sendGesture('wave')">&#128075; Wave</button>
                <button class="btn btn-gesture" onclick="sendGesture('point')">&#128073; Point</button>
                <button class="btn btn-gesture" onclick="sendGesture('handshake')">&#129309; Handshake</button>
                <button class="btn btn-gesture" onclick="sendGesture('rest')">&#128564; Rest</button>
            </div>
        </div>

        <div class="status">
            <h3>Status</h3>
            <div class="distance">Distance: <span id="distance">--</span> cm</div>
            <div>Connected Devices: <span id="wifiCount">--</span></div>
            <div>Uptime: <span id="uptime">--</span></div>
        </div>
    </div>

    <script>
        function sendMove(direction) {
            fetch('/api/move', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: 'direction=' + direction
            });
        }

        function updateServo(servo, position) {
            document.getElementById(servo + 'Value').textContent = position;
            fetch('/api/servo', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: 'servo=' + servo + '&position=' + position
            });
        }

        function sendGesture(gesture) {
            fetch('/api/gesture', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: 'gesture=' + gesture
            });
        }

        function updateStatus() {
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('distance').textContent = data.distance.toFixed(1);
                    document.getElementById('wifiCount').textContent = data.wifi;
                    document.getElementById('uptime').textContent = Math.floor(data.uptime / 1000) + 's';
                });
        }

        // Update status every 2 seconds
        setInterval(updateStatus, 2000);
        updateStatus();
    </script>
</body>
</html>
)HTML";
}

// ===== STATUS LED =====
void updateStatusLED() {
  static unsigned long lastBlink = 0;
  static bool ledState = false;

  if (millis() - lastBlink > 1000) {
    ledState = !ledState;
    digitalWrite(STATUS_LED, ledState);
    lastBlink = millis();
  }
}
