<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="200dp"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:id="@+id/preset_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Preset Name"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:layout_marginBottom="8dp" />

        <TextView
            android:id="@+id/preset_description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Preset description"
            android:textSize="14sp"
            android:textColor="@color/text_secondary"
            android:layout_marginBottom="8dp"
            android:maxLines="2"
            android:ellipsize="end" />

        <TextView
            android:id="@+id/command_count"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="3 commands"
            android:textSize="12sp"
            android:textColor="@color/text_secondary"
            android:layout_marginBottom="12dp" />

        <Button
            android:id="@+id/execute_button"
            style="@style/ButtonPrimary"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Execute"
            android:textSize="14sp" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
