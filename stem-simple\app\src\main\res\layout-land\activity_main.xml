<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary"
    tools:context=".MainActivity">

    <!-- Compact Status Bar -->
    <LinearLayout
        android:id="@+id/status_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/primary_blue"
        android:orientation="horizontal"
        android:padding="2dp"
        android:gravity="center_vertical"
        app:layout_constraintTop_toTopOf="parent">

        <!-- Connection Status -->
        <TextView
            android:id="@+id/connection_status"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Robot"
            android:textColor="@android:color/white"
            android:textSize="9sp"
            android:textStyle="bold" />

        <!-- Face Count Display -->
        <TextView
            android:id="@+id/face_count_display"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:text="👤0"
            android:textColor="@android:color/white"
            android:textSize="9sp" />

        <!-- Distance Display -->
        <TextView
            android:id="@+id/distance_display"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:text="📏--"
            android:textColor="@android:color/white"
            android:textSize="9sp" />

        <!-- Mini Camera Preview -->
        <androidx.camera.view.PreviewView
            android:id="@+id/mini_camera_preview"
            android:layout_width="40dp"
            android:layout_height="30dp"
            android:layout_marginStart="4dp"
            android:background="@color/surface_light"
            android:scaleType="centerCrop" />

    </LinearLayout>

    <!-- Main Content Area with Tabs -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintTop_toBottomOf="@id/status_bar"
        app:layout_constraintBottom_toBottomOf="parent">

        <!-- Tab Navigation - Vertical for landscape -->
        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tab_layout"
            android:layout_width="80dp"
            android:layout_height="match_parent"
            android:background="@color/surface_light"
            app:tabMode="fixed"
            app:tabGravity="fill"
            app:tabTextColor="@color/text_secondary"
            app:tabSelectedTextColor="@color/primary_blue"
            app:tabIndicatorColor="@color/primary_blue"
            app:tabIndicatorHeight="3dp"
            android:orientation="vertical" />

        <!-- ViewPager for Tab Content -->
        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/view_pager"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
